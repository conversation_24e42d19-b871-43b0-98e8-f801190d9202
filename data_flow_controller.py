#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据流控制器
解决MQTT高频数据与大模型低频处理的速率不匹配问题
"""
import asyncio
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import deque
from enum import Enum
from queue import PriorityQueue
import requests

# 导入现有的数据结构
from battlefield_simulator import BattlefieldUnit

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DataPriority(Enum):
    """数据优先级枚举"""
    LOW = 3
    MEDIUM = 2
    HIGH = 1
    CRITICAL = 0

@dataclass
class BattlefieldSnapshot:
    """战场快照数据"""
    timestamp: str
    enemy_units: List[BattlefieldUnit]
    friendly_units: List[BattlefieldUnit]
    priority: DataPriority
    change_score: float  # 变化程度评分
    threat_level: str
    
    def __lt__(self, other):
        """用于优先队列排序"""
        return self.priority.value < other.priority.value

class DataFlowController:
    """数据流控制器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化数据流控制器
        
        Args:
            config: 配置参数
        """
        # 默认配置
        default_config = {
            "buffer_size": 100,  # 缓冲区大小
            "ai_analysis_interval": 30,  # AI分析间隔(秒)
            "frontend_update_interval": 1,  # 前端更新间隔(秒)
            "change_threshold": 0.3,  # 变化阈值
            "max_concurrent_ai_tasks": 2,  # 最大并发AI任务数
            "api_url": "http://localhost:8000"
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 数据缓冲区
        self.data_buffer = deque(maxlen=self.config["buffer_size"])
        self.priority_queue = PriorityQueue()
        
        # 状态管理
        self.last_ai_analysis_time = 0
        self.last_frontend_update_time = 0
        self.last_snapshot: Optional[BattlefieldSnapshot] = None
        self.running_ai_tasks = 0
        
        # 统计信息
        self.stats = {
            "total_received": 0,
            "ai_analyses_triggered": 0,
            "frontend_updates_sent": 0,
            "data_dropped": 0,
            "start_time": time.time()
        }
        
        # 异步任务管理
        self.ai_task_queue = asyncio.Queue()
        self.frontend_update_queue = asyncio.Queue()
        
        logger.info("数据流控制器初始化完成")
    
    def calculate_change_score(self, current_units: List[BattlefieldUnit], 
                             previous_units: List[BattlefieldUnit] = None) -> float:
        """
        计算战场变化程度评分
        
        Args:
            current_units: 当前单位列表
            previous_units: 之前单位列表
            
        Returns:
            变化评分 (0-1)
        """
        if not previous_units:
            return 1.0  # 首次数据，认为是重大变化
        
        # 计算单位数量变化
        unit_count_change = abs(len(current_units) - len(previous_units)) / max(len(current_units), len(previous_units), 1)
        
        # 计算位置变化
        position_changes = 0
        current_positions = {unit.id: (unit.position.longitude, unit.position.latitude) for unit in current_units}
        previous_positions = {unit.id: (unit.position.longitude, unit.position.latitude) for unit in previous_units}
        
        common_units = set(current_positions.keys()) & set(previous_positions.keys())
        if common_units:
            for unit_id in common_units:
                curr_pos = current_positions[unit_id]
                prev_pos = previous_positions[unit_id]
                distance = ((curr_pos[0] - prev_pos[0])**2 + (curr_pos[1] - prev_pos[1])**2)**0.5
                if distance > 0.001:  # 位置变化阈值
                    position_changes += 1
            
            position_change_ratio = position_changes / len(common_units)
        else:
            position_change_ratio = 1.0
        
        # 综合评分
        change_score = min(1.0, unit_count_change * 0.6 + position_change_ratio * 0.4)
        return change_score
    
    def determine_priority(self, snapshot: BattlefieldSnapshot) -> DataPriority:
        """
        确定数据优先级
        
        Args:
            snapshot: 战场快照
            
        Returns:
            数据优先级
        """
        # 基于威胁等级和变化程度确定优先级
        high_threat_count = len([u for u in snapshot.enemy_units if u.threat_level in ["高", "极高"]])
        total_enemies = len(snapshot.enemy_units)
        
        if high_threat_count > 0 and snapshot.change_score > 0.7:
            return DataPriority.CRITICAL
        elif high_threat_count > total_enemies * 0.5 or snapshot.change_score > 0.5:
            return DataPriority.HIGH
        elif snapshot.change_score > 0.3:
            return DataPriority.MEDIUM
        else:
            return DataPriority.LOW
    
    async def process_mqtt_data(self, enemy_units: List[BattlefieldUnit], 
                               friendly_units: List[BattlefieldUnit] = None) -> None:
        """
        处理MQTT接收到的数据
        
        Args:
            enemy_units: 敌方单位列表
            friendly_units: 我方单位列表
        """
        self.stats["total_received"] += 1
        
        if friendly_units is None:
            friendly_units = []
        
        # 计算变化程度
        previous_units = self.last_snapshot.enemy_units if self.last_snapshot else []
        change_score = self.calculate_change_score(enemy_units, previous_units)
        
        # 创建战场快照
        snapshot = BattlefieldSnapshot(
            timestamp=datetime.now().isoformat(),
            enemy_units=enemy_units,
            friendly_units=friendly_units,
            priority=DataPriority.MEDIUM,  # 临时设置，后面会重新计算
            change_score=change_score,
            threat_level=self._assess_threat_level(enemy_units)
        )
        
        # 确定优先级
        snapshot.priority = self.determine_priority(snapshot)
        
        # 添加到缓冲区
        self.data_buffer.append(snapshot)
        self.priority_queue.put(snapshot)
        
        logger.debug(f"数据已缓冲 - 优先级: {snapshot.priority.name}, 变化评分: {change_score:.3f}")
        
        # 检查是否需要触发AI分析
        await self._check_ai_analysis_trigger(snapshot)
        
        # 检查是否需要更新前端
        await self._check_frontend_update_trigger(snapshot)
        
        self.last_snapshot = snapshot
    
    def _assess_threat_level(self, enemy_units: List[BattlefieldUnit]) -> str:
        """评估威胁等级"""
        if not enemy_units:
            return "无威胁"
        
        high_threat_count = len([u for u in enemy_units if u.threat_level in ["高", "极高"]])
        total_count = len(enemy_units)
        
        if high_threat_count > total_count * 0.7:
            return "极高威胁"
        elif high_threat_count > total_count * 0.4:
            return "高威胁"
        elif high_threat_count > 0:
            return "中等威胁"
        else:
            return "低威胁"
    
    async def _check_ai_analysis_trigger(self, snapshot: BattlefieldSnapshot) -> None:
        """检查是否需要触发AI分析"""
        current_time = time.time()
        time_since_last = current_time - self.last_ai_analysis_time
        
        # 触发条件：
        # 1. 时间间隔达到要求
        # 2. 高优先级数据且当前AI任务不多
        # 3. 关键数据立即触发
        should_trigger = (
            time_since_last >= self.config["ai_analysis_interval"] or
            (snapshot.priority in [DataPriority.HIGH, DataPriority.CRITICAL] and 
             self.running_ai_tasks < self.config["max_concurrent_ai_tasks"]) or
            snapshot.priority == DataPriority.CRITICAL
        )
        
        if should_trigger:
            await self.ai_task_queue.put(snapshot)
            self.last_ai_analysis_time = current_time
            logger.info(f"触发AI分析 - 优先级: {snapshot.priority.name}")
    
    async def _check_frontend_update_trigger(self, snapshot: BattlefieldSnapshot) -> None:
        """检查是否需要更新前端"""
        current_time = time.time()
        time_since_last = current_time - self.last_frontend_update_time
        
        # 前端更新频率控制
        if time_since_last >= self.config["frontend_update_interval"]:
            await self.frontend_update_queue.put(snapshot)
            self.last_frontend_update_time = current_time
            logger.debug("触发前端更新")
    
    async def ai_analysis_worker(self):
        """AI分析工作协程"""
        while True:
            try:
                snapshot = await self.ai_task_queue.get()
                self.running_ai_tasks += 1
                self.stats["ai_analyses_triggered"] += 1
                
                logger.info(f"开始AI分析 - 敌方单位: {len(snapshot.enemy_units)}个")
                
                # 构建分析数据
                analysis_data = {
                    "timestamp": snapshot.timestamp,
                    "simulation_time": (time.time() - self.stats["start_time"]) / 60.0,
                    "enemy_units": [asdict(unit) for unit in snapshot.enemy_units],
                    "friendly_units": [asdict(unit) for unit in snapshot.friendly_units],
                    "battlefield_status": {
                        "total_enemy": len(snapshot.enemy_units),
                        "total_friendly": len(snapshot.friendly_units),
                        "threat_level": snapshot.threat_level,
                        "change_score": snapshot.change_score
                    }
                }
                
                # 发送到AI分析API
                await self._send_ai_analysis_request(analysis_data)
                
            except Exception as e:
                logger.error(f"AI分析工作协程错误: {e}")
            finally:
                self.running_ai_tasks -= 1
                self.ai_task_queue.task_done()
    
    async def frontend_update_worker(self):
        """前端更新工作协程"""
        while True:
            try:
                snapshot = await self.frontend_update_queue.get()
                self.stats["frontend_updates_sent"] += 1
                
                # 构建前端数据
                frontend_data = {
                    "timestamp": snapshot.timestamp,
                    "enemy_units": [asdict(unit) for unit in snapshot.enemy_units],
                    "friendly_units": [asdict(unit) for unit in snapshot.friendly_units],
                    "battlefield_status": {
                        "total_enemy": len(snapshot.enemy_units),
                        "total_friendly": len(snapshot.friendly_units),
                        "threat_level": snapshot.threat_level
                    }
                }
                
                # 发送到前端API
                await self._send_frontend_update(frontend_data)
                
            except Exception as e:
                logger.error(f"前端更新工作协程错误: {e}")
            finally:
                self.frontend_update_queue.task_done()
    
    async def _send_ai_analysis_request(self, data: Dict[str, Any]):
        """发送AI分析请求"""
        try:
            response = requests.post(
                f"{self.config['api_url']}/battlefield/analyze",
                json=data,
                timeout=60  # AI分析可能需要较长时间
            )
            if response.status_code == 200:
                logger.info(f"✅ AI分析请求成功 - 响应时间: {response.elapsed.total_seconds():.2f}s")
            else:
                logger.error(f"❌ AI分析请求失败 - 状态码: {response.status_code}")
        except Exception as e:
            logger.error(f"❌ AI分析请求异常: {e}")
    
    async def _send_frontend_update(self, data: Dict[str, Any]):
        """发送前端更新"""
        try:
            response = requests.post(
                f"{self.config['api_url']}/threed/units",
                json=data,
                timeout=5
            )
            if response.status_code == 200:
                logger.debug("✅ 前端数据更新成功")
            else:
                logger.warning(f"❌ 前端数据更新失败 - 状态码: {response.status_code}")
        except Exception as e:
            logger.warning(f"❌ 前端数据更新异常: {e}")
    
    def get_latest_snapshot(self) -> Optional[BattlefieldSnapshot]:
        """获取最新的战场快照"""
        return self.last_snapshot
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        runtime = time.time() - self.stats["start_time"]
        return {
            **self.stats,
            "runtime_seconds": runtime,
            "buffer_size": len(self.data_buffer),
            "pending_ai_tasks": self.ai_task_queue.qsize(),
            "pending_frontend_updates": self.frontend_update_queue.qsize(),
            "running_ai_tasks": self.running_ai_tasks
        }
    
    def print_stats(self):
        """打印统计信息"""
        stats = self.get_stats()
        logger.info(f"📊 数据流控制器统计 - 运行时间: {stats['runtime_seconds']:.1f}s, "
                   f"接收数据: {stats['total_received']}, AI分析: {stats['ai_analyses_triggered']}, "
                   f"前端更新: {stats['frontend_updates_sent']}, 缓冲区: {stats['buffer_size']}")
