#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
只测试我方单位创建
"""
import sys
import os
from datetime import datetime
from dataclasses import dataclass
from typing import List

@dataclass
class Position:
    longitude: float
    latitude: float
    altitude: float

@dataclass
class WeaponLoadout:
    weapon_type: str
    quantity: int
    max_quantity: int

@dataclass
class UnitStatus:
    health: float
    fuel: float
    operational: bool
    weapons: List[WeaponLoadout]

@dataclass
class BattlefieldUnit:
    id: str
    name: str
    type: str
    side: str
    position: Position
    status: UnitStatus
    threat_level: str
    confidence: float
    last_seen: str
    speed: float
    heading: float

def create_test_units():
    """创建测试单位"""
    print("开始创建测试单位...")
    
    units = []
    for i in range(3):  # 只创建3个测试
        unit = BattlefieldUnit(
            id=str(i),
            name="直-10",
            type="武装直升机",
            side="我方",
            position=Position(116.0 + i*0.001, 40.0 + i*0.001, 1000.0),
            status=UnitStatus(100.0, 100.0, True, []),
            threat_level="我方",
            confidence=1.0,
            last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            speed=0.0,
            heading=0.0
        )
        units.append(unit)
        print(f"创建单位: ID={unit.id}, 名称={unit.name}")
    
    print(f"总共创建了 {len(units)} 个单位")
    return units

if __name__ == "__main__":
    print("测试开始...")
    try:
        units = create_test_units()
        print("测试成功!")
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    print("测试结束")
