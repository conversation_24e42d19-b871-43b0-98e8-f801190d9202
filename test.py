import asyncio
import websockets
import json

async def receive_messages():
    """
    连接WebSocket服务器并接收消息
    """
    uri = "ws://localhost:8010/ws/threed"  # WebSocket服务器地址
    
    try:
        async with websockets.connect(uri) as websocket:
            print(f"已连接到 WebSocket 服务器: {uri}")
            
            # 发送一条初始消息（可选）
            await websocket.send(json.dumps({"type": "greeting", "message": "Hello Server!"}))
            
            # 持续接收消息
            while True:
                try:
                    # 接收消息，设置超时时间
                    message = await asyncio.wait_for(websocket.recv(), timeout=60.0)
                    print(f"收到消息: {message}")
                    
                    # 如果是JSON格式的消息，可以解析
                    try:
                        data = json.loads(message)
                        print(f"解析后的数据: {json.dumps(data, indent=4, ensure_ascii=False)}")
                    except json.JSONDecodeError:
                        pass  # 如果不是JSON格式，保持原样显示
                        
                except asyncio.TimeoutError:
                    print("接收消息超时，发送心跳...")
                    # 发送心跳保持连接
                    await websocket.send(json.dumps({"type": "ping"}))
                    
    except websockets.exceptions.ConnectionClosed:
        print("WebSocket连接已关闭")
    except Exception as e:
        print(f"连接错误: {e}")

# 运行客户端
if __name__ == "__main__":
    asyncio.run(receive_messages())