# 数据流控制器使用说明

## 概述

数据流控制器（DataFlowController）是为了解决MQTT高频数据（每秒15次）与大模型低频处理（30秒/次）和前端渲染限制之间的速率不匹配问题而设计的。

## 核心功能

### 1. 数据缓冲与优先级管理
- **缓冲队列**：缓存高频MQTT数据，避免数据丢失
- **优先级队列**：根据威胁等级和变化程度智能排序
- **智能采样**：选择最重要的数据进行AI分析

### 2. 异步任务调度
- **AI分析工作协程**：管理大模型分析任务，避免并发过载
- **前端更新工作协程**：控制前端渲染频率，保证流畅性
- **并发控制**：限制同时运行的AI任务数量

### 3. 变化检测与评分
- **位置变化检测**：监控单位移动
- **数量变化检测**：监控单位增减
- **威胁等级评估**：综合评估战场威胁程度

## 系统架构

```
MQTT数据(15Hz) → 数据流控制器 → AI分析(30s间隔) → 战术建议
                     ↓
                前端更新(1Hz) → 3D渲染
```

## 配置参数

### 主要配置项（config/data_flow_config.json）

```json
{
  "data_flow_controller": {
    "buffer_size": 100,                    // 缓冲区大小
    "ai_analysis_interval": 30,            // AI分析间隔(秒)
    "frontend_update_interval": 1,         // 前端更新间隔(秒)
    "change_threshold": 0.3,               // 变化阈值
    "max_concurrent_ai_tasks": 2,          // 最大并发AI任务数
    "api_url": "http://localhost:8000"     // API服务地址
  }
}
```

## 使用方法

### 1. 集成到MQTT接收器

已经集成到 `mqtt_data_receiver.py` 中：

```python
# 初始化时创建数据流控制器
self.flow_controller = DataFlowController(flow_controller_config)

# 在消息回调中使用
def on_message(self, client, userdata, msg):
    enemy_units = self._parse_mqtt_data(payload)
    if enemy_units:
        # 提交到数据流控制器
        asyncio.create_task(self.flow_controller.process_mqtt_data(enemy_units, []))
```

### 2. 启动系统

```bash
# 启动MQTT接收器（已集成数据流控制器）
python mqtt_data_receiver.py
```

### 3. 测试高频数据处理

```bash
# 运行高频数据测试
python test_high_frequency_data.py
```

## 优先级策略

### 数据优先级分类

1. **CRITICAL（关键）**：高威胁单位 + 重大变化（变化评分 > 0.7）
2. **HIGH（高）**：高威胁单位比例 > 50% 或 变化评分 > 0.5
3. **MEDIUM（中）**：变化评分 > 0.3
4. **LOW（低）**：其他情况

### 触发条件

#### AI分析触发条件：
- 时间间隔达到设定值（30秒）
- 高优先级数据且当前AI任务数未达上限
- 关键数据立即触发

#### 前端更新触发条件：
- 时间间隔达到设定值（1秒）

## 性能优化

### 1. 内存管理
- 使用固定大小的deque作为缓冲区
- 自动清理过期数据
- 优先队列自动排序

### 2. 并发控制
- 限制同时运行的AI分析任务数
- 异步处理，避免阻塞MQTT接收
- 独立的工作协程处理不同任务

### 3. 网络优化
- AI分析请求超时60秒
- 前端更新请求超时5秒
- 失败重试机制

## 监控与统计

### 统计信息包括：
- 接收数据总数
- AI分析触发次数
- 前端更新次数
- 缓冲区状态
- 运行时间和处理速率

### 查看统计：
```python
# 获取统计信息
stats = flow_controller.get_stats()

# 打印统计信息
flow_controller.print_stats()
```

## 故障排除

### 常见问题

1. **AI分析响应慢**
   - 检查大模型服务状态
   - 调整 `max_concurrent_ai_tasks` 参数
   - 增加 `ai_analysis_interval` 间隔

2. **前端渲染卡顿**
   - 调整 `frontend_update_interval` 参数
   - 检查前端处理能力
   - 减少传输的数据量

3. **数据丢失**
   - 增加 `buffer_size` 大小
   - 检查处理速度是否跟上接收速度
   - 调整优先级策略

### 日志级别
- DEBUG：详细的数据流信息
- INFO：重要事件和统计信息
- WARNING：非关键错误
- ERROR：严重错误

## 扩展功能

### 1. 自定义优先级策略
可以重写 `determine_priority` 方法来实现自定义的优先级逻辑。

### 2. 自定义变化检测
可以重写 `calculate_change_score` 方法来实现更复杂的变化检测算法。

### 3. 添加新的触发条件
可以在 `_check_ai_analysis_trigger` 和 `_check_frontend_update_trigger` 方法中添加新的触发逻辑。

## 性能指标

### 预期性能：
- **输入处理能力**：15Hz（每秒15次MQTT消息）
- **AI分析频率**：每30秒一次（可配置）
- **前端更新频率**：1Hz（每秒一次）
- **内存使用**：缓冲区100条记录约占用几MB
- **CPU使用**：主要消耗在数据转换和网络请求

### 实际测试结果：
运行 `test_high_frequency_data.py` 可以获得实际的性能数据。
