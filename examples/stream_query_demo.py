"""
流式查询演示 - 模拟图像识别结果的实时查询
"""
import json
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.stream_query_service import StreamQueryService


def create_mock_recognition_results():
    """创建模拟的图像识别结果"""
    
    # 情况1: 识别到单个对象
    single_object = {
        "image_id": "img_001",
        "timestamp": time.time(),
        "status": "success",
        "confidence": 0.95,
        "detected_objects": [
            {
                "name": "歼-20 战斗机",
                "confidence": 0.95,
                "bbox": [100, 100, 300, 200],
                "class_id": 1
            }
        ]
    }
    
    # 情况2: 识别到多个对象
    multiple_objects = {
        "image_id": "img_002", 
        "timestamp": time.time(),
        "status": "success",
        "confidence": 0.88,
        "detected_objects": [
            {
                "name": "歼-20 战斗机",
                "confidence": 0.92,
                "bbox": [50, 50, 200, 150],
                "class_id": 1
            },
            {
                "name": "F-35 闪电 II",
                "confidence": 0.85,
                "bbox": [250, 100, 400, 200],
                "class_id": 2
            },
            {
                "name": "不存在的飞机",
                "confidence": 0.78,
                "bbox": [450, 120, 600, 220],
                "class_id": 3
            }
        ]
    }
    
    # 情况3: 空结果（未识别到任何对象）
    empty_result = {
        "image_id": "img_003",
        "timestamp": time.time(),
        "status": "no_detection",
        "confidence": 0.0,
        "detected_objects": []
    }
    
    # 情况4: 低置信度结果（会被过滤）
    low_confidence = {
        "image_id": "img_004",
        "timestamp": time.time(),
        "status": "low_confidence",
        "confidence": 0.3,
        "detected_objects": [
            {
                "name": "歼-20 战斗机",
                "confidence": 0.3,  # 低于阈值
                "bbox": [100, 100, 300, 200],
                "class_id": 1
            }
        ]
    }
    
    # 情况5: 混合置信度（部分会被过滤）
    mixed_confidence = {
        "image_id": "img_005",
        "timestamp": time.time(),
        "status": "mixed",
        "confidence": 0.7,
        "detected_objects": [
            {
                "name": "歼-20 战斗机",
                "confidence": 0.9,  # 高置信度，会被保留
                "bbox": [100, 100, 300, 200],
                "class_id": 1
            },
            {
                "name": "F-35 闪电 II",
                "confidence": 0.3,  # 低置信度，会被过滤
                "bbox": [350, 100, 500, 200],
                "class_id": 2
            }
        ]
    }
    
    return [single_object, multiple_objects, empty_result, low_confidence, mixed_confidence]


def demo_single_stream_query():
    """演示单个流式查询"""
    print("=== 单个流式查询演示 ===\n")
    
    try:
        stream_service = StreamQueryService()
        mock_results = create_mock_recognition_results()
        
        for i, recognition_data in enumerate(mock_results, 1):
            print(f"测试 {i}: {recognition_data['status']} - 图像ID: {recognition_data['image_id']}")
            print(f"   识别对象数量: {len(recognition_data['detected_objects'])}")
            
            # 执行流式查询
            result = stream_service.stream_query(recognition_data)
            
            print(f"   查询状态: {result['status']}")
            print(f"   处理时间: {result['processing_time']}ms")
            print(f"   查询数量: {result['query_count']}")
            
            if result['query_results']:
                query_data = result['query_results']
                print(f"   找到记录: {query_data['total_found']}")
                print(f"   未找到记录: {len(query_data['not_found'])}")
                
                # 显示找到的记录
                if query_data['found']:
                    print("   找到的对象:")
                    for name, info in query_data['found'].items():
                        print(f"     - {name}: {info['category']}")
            
            print()
        
    except Exception as e:
        print(f"✗ 单个流式查询演示失败: {str(e)}")


def demo_batch_stream_query():
    """演示批量流式查询"""
    print("=== 批量流式查询演示 ===\n")
    
    try:
        stream_service = StreamQueryService()
        mock_results = create_mock_recognition_results()
        
        print(f"批量处理 {len(mock_results)} 个图像识别结果...")
        
        # 执行批量流式查询
        batch_results = stream_service.batch_stream_query(mock_results)
        
        print(f"批量查询完成，处理了 {len(batch_results)} 个结果\n")
        
        # 统计结果
        success_count = sum(1 for r in batch_results if r['status'] in ['success', 'empty', 'not_found'])
        error_count = sum(1 for r in batch_results if r['status'] == 'error')
        total_found = sum(r.get('query_results', {}).get('total_found', 0) for r in batch_results if r.get('query_results'))
        
        print(f"批量查询统计:")
        print(f"   成功处理: {success_count}")
        print(f"   处理失败: {error_count}")
        print(f"   总找到记录: {total_found}")
        
        # 显示详细结果
        print("\n详细结果:")
        for result in batch_results:
            print(f"   图像 {result.get('image_id', 'unknown')}: {result['status']} "
                  f"({result.get('processing_time', 0)}ms)")
        
    except Exception as e:
        print(f"✗ 批量流式查询演示失败: {str(e)}")


def demo_real_time_simulation():
    """演示实时流式查询模拟"""
    print("=== 实时流式查询模拟 ===\n")
    
    try:
        stream_service = StreamQueryService()
        
        print("模拟实时图像识别流...")
        print("(每秒处理一个图像识别结果)\n")
        
        # 模拟实时数据流
        for i in range(5):
            # 动态生成识别结果
            recognition_data = {
                "image_id": f"realtime_img_{i+1}",
                "timestamp": time.time(),
                "status": "success",
                "confidence": 0.8 + (i * 0.05),
                "detected_objects": [
                    {
                        "name": "歼-20 战斗机" if i % 2 == 0 else "F-35 闪电 II",
                        "confidence": 0.8 + (i * 0.05),
                        "bbox": [100 + i*10, 100 + i*10, 300 + i*10, 200 + i*10],
                        "class_id": i + 1
                    }
                ]
            }
            
            print(f"处理实时图像 {i+1}...")
            
            # 执行流式查询
            result = stream_service.stream_query(recognition_data)
            
            print(f"   状态: {result['status']}")
            print(f"   处理时间: {result['processing_time']}ms")
            
            if result['query_results'] and result['query_results']['found']:
                for name in result['query_results']['found'].keys():
                    print(f"   识别并查询到: {name}")
            
            # 模拟实时间隔
            time.sleep(1)
        
        # 显示统计信息
        print("\n实时查询统计:")
        stats = stream_service.get_stream_stats()
        for key, value in stats.items():
            print(f"   {key}: {value}")
        
    except Exception as e:
        print(f"✗ 实时流式查询模拟失败: {str(e)}")


def demo_json_formats():
    """演示不同的JSON输入格式"""
    print("=== JSON格式演示 ===\n")
    
    try:
        stream_service = StreamQueryService()
        
        # 格式1: JSON字符串
        print("1. JSON字符串格式:")
        json_str = json.dumps({
            "image_id": "json_test_1",
            "timestamp": time.time(),
            "status": "success",
            "detected_objects": [
                {"name": "歼-20 战斗机", "confidence": 0.9}
            ]
        })
        
        result1 = stream_service.stream_query(json_str)
        print(f"   状态: {result1['status']}")
        print(f"   处理时间: {result1['processing_time']}ms")
        
        # 格式2: Python字典
        print("\n2. Python字典格式:")
        dict_data = {
            "image_id": "json_test_2",
            "timestamp": time.time(),
            "status": "success",
            "detected_objects": [
                {"name": "F-35 闪电 II", "confidence": 0.85}
            ]
        }
        
        result2 = stream_service.stream_query(dict_data)
        print(f"   状态: {result2['status']}")
        print(f"   处理时间: {result2['processing_time']}ms")
        
        # 格式3: 简化格式（自动补全）
        print("\n3. 简化格式（自动补全）:")
        simple_data = {
            "detected_objects": [
                {"name": "歼-20 战斗机", "confidence": 0.95}
            ]
        }
        
        result3 = stream_service.stream_query(simple_data)
        print(f"   状态: {result3['status']}")
        print(f"   自动生成图像ID: {result3['image_id']}")
        print(f"   处理时间: {result3['processing_time']}ms")
        
    except Exception as e:
        print(f"✗ JSON格式演示失败: {str(e)}")


def main():
    """主函数"""
    print("流式查询功能演示")
    print("=" * 50)
    
    # 单个流式查询演示
    demo_single_stream_query()
    
    # 批量流式查询演示
    demo_batch_stream_query()
    
    # 实时流式查询模拟
    demo_real_time_simulation()
    
    # JSON格式演示
    demo_json_formats()
    
    print("\n" + "=" * 50)
    print("演示结束")
    print("\n流式查询特点:")
    print("1. 自动处理空结果、单个对象、多个对象")
    print("2. 置信度过滤（阈值: 0.5）")
    print("3. 实时性能监控和统计")
    print("4. 支持批量处理")
    print("5. 标准化的返回格式")


if __name__ == "__main__":
    main()
