"""
战场AI分析Pipeline完整演示
展示从流式JSON数据到LLM战术分析的完整流程
"""
import json
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.battlefield_ai_pipeline import BattlefieldAIPipeline


def create_sample_battlefield_json_data():
    """创建符合需求格式的示例战场JSON数据"""
    
    # 示例1: 敌方主战坦克
    enemy_tank_data = {
        "ID": "ENEMY_001",
        "武器名称": "T-90主战坦克",
        "武器类型": "主战坦克",
        "精度": 116.3974,  # 经度
        "纬度": 39.9042,   # 纬度 
        "高度": 50.5,      # 高度(米)
        "属方": "敌方"
    }
    
    # 示例2: 敌方装甲车
    enemy_apc_data = {
        "ID": "ENEMY_002", 
        "武器名称": "BTR-80装甲运兵车",
        "武器类型": "装甲车",
        "精度": 116.4074,
        "纬度": 39.9142,
        "高度": 48.2,
        "属方": "敌方"
    }
    
    # 示例3: 我方战斗机
    friendly_fighter_data = {
        "ID": "FRIENDLY_001",
        "武器名称": "歼-20战斗机", 
        "武器类型": "战斗机",
        "精度": 116.3874,
        "纬度": 39.8942,
        "高度": 8000.0,
        "属方": "我方"
    }
    
    # 示例4: 未知武器
    unknown_weapon_data = {
        "ID": "UNKNOWN_001",
        "武器名称": "未知装甲目标",
        "武器类型": "装甲车",
        "精度": 116.4174,
        "纬度": 39.9242,
        "高度": 52.1,
        "属方": "敌方"
    }
    
    return [enemy_tank_data, enemy_apc_data, friendly_fighter_data, unknown_weapon_data]


def demo_single_pipeline_analysis():
    """演示单条数据的完整Pipeline分析"""
    print("=== 单条数据Pipeline分析演示 ===\n")
    
    try:
        # 初始化Pipeline
        pipeline = BattlefieldAIPipeline()
        print("✓ 战场AI Pipeline初始化成功")
        
        # 测试连接性
        connectivity = pipeline.test_pipeline_connectivity()
        print(f"✓ 系统连接状态: {connectivity['overall_status']}")
        
        if connectivity['overall_status'] != "正常":
            print("⚠️  部分组件连接异常，但可以继续演示")
        
        # 获取示例数据
        sample_data = create_sample_battlefield_json_data()
        
        # 处理第一个示例（敌方坦克）
        print(f"\n--- 处理敌方坦克数据 ---")
        tank_data = sample_data[0]
        print(f"输入数据: {json.dumps(tank_data, ensure_ascii=False)}")
        
        # 执行完整Pipeline
        result = pipeline.process_single_battlefield_data(tank_data)
        
        if result["status"] == "pipeline_completed":
            print("✅ Pipeline处理完成")
            
            # 显示处理结果摘要
            print(f"\n📊 处理结果摘要:")
            print(f"  Pipeline ID: {result['pipeline_id']}")
            print(f"  总处理时间: {result['total_processing_time']}ms")
            
            # 显示输入数据信息
            input_data = result["input_data"]
            print(f"\n🎯 输入数据:")
            print(f"  战场ID: {input_data['battlefield_id']}")
            print(f"  武器名称: {input_data['weapon_name']}")
            print(f"  武器类型: {input_data['weapon_type']}")
            print(f"  属方: {input_data['faction']}")
            print(f"  位置: ({input_data['location']['longitude']}, {input_data['location']['latitude']})")
            
            # 显示情报数据
            intel_data = result["intelligence_data"]
            print(f"\n🔍 情报数据:")
            print(f"  武器知识库状态: {intel_data['weapon_knowledge_status']}")
            print(f"  我方部队数量: {intel_data['friendly_forces_count']}")
            print(f"  威胁评估: {intel_data.get('threat_assessment', {}).get('threat_level', '未评估')}")
            
            # 显示AI分析结果
            ai_analysis = result["ai_analysis"]
            print(f"\n🧠 AI分析:")
            print(f"  LLM提供商: {ai_analysis['llm_provider']}")
            print(f"  使用模型: {ai_analysis['model']}")
            print(f"  消耗Tokens: {ai_analysis['tokens_used']}")
            
            # 显示战术建议
            tactical = result["tactical_recommendations"]
            if tactical["status"] == "structured_response":
                summary = tactical["summary"]
                print(f"\n⚔️  战术建议:")
                print(f"  威胁等级: {summary['threat_level']}")
                print(f"  主要策略: {summary['primary_strategy']}")
                print(f"  成功概率: {summary['success_probability']}")
                print(f"  任务时长: {summary['mission_duration']}")
                
                if summary['key_tactics']:
                    print(f"  关键战术:")
                    for tactic in summary['key_tactics']:
                        print(f"    • {tactic}")
            
            # 显示性能指标
            performance = result["performance_metrics"]
            print(f"\n⚡ 性能指标:")
            print(f"  数据处理: {performance['data_processing_time']}ms")
            print(f"  LLM分析: {performance['llm_analysis_time']}ms")
            print(f"  Pipeline效率: {performance['pipeline_efficiency']}")
            
        else:
            print(f"❌ Pipeline处理失败: {result['error_message']}")
            
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")


def demo_batch_pipeline_processing():
    """演示批量Pipeline处理"""
    print("\n=== 批量Pipeline处理演示 ===\n")
    
    try:
        pipeline = BattlefieldAIPipeline()
        sample_data = create_sample_battlefield_json_data()
        
        print(f"批量处理 {len(sample_data)} 条战场数据...")
        
        # 批量处理
        results = pipeline.process_battlefield_stream(sample_data)
        
        print(f"\n📊 批量处理结果:")
        print(f"  总数据量: {len(sample_data)}")
        print(f"  处理结果数: {len(results)}")
        
        # 统计结果
        successful = sum(1 for r in results if r.get("status") == "pipeline_completed")
        failed = len(results) - successful
        
        print(f"  成功处理: {successful}")
        print(f"  处理失败: {failed}")
        print(f"  成功率: {successful/len(results)*100:.1f}%")
        
        # 显示每个结果的摘要
        print(f"\n📋 处理结果详情:")
        for i, result in enumerate(results, 1):
            if result.get("status") == "pipeline_completed":
                input_data = result["input_data"]
                tactical = result["tactical_recommendations"]
                
                print(f"  {i}. {input_data['weapon_name']} ({input_data['faction']})")
                print(f"     处理时间: {result['total_processing_time']}ms")
                
                if tactical["status"] == "structured_response":
                    summary = tactical["summary"]
                    print(f"     威胁等级: {summary['threat_level']}")
                    print(f"     主要策略: {summary['primary_strategy']}")
                else:
                    print(f"     分析状态: {tactical['status']}")
            else:
                print(f"  {i}. 处理失败: {result.get('error_message', '未知错误')}")
        
    except Exception as e:
        print(f"❌ 批量处理演示失败: {str(e)}")


def demo_pipeline_performance():
    """演示Pipeline性能监控"""
    print("\n=== Pipeline性能监控演示 ===\n")
    
    try:
        pipeline = BattlefieldAIPipeline()
        sample_data = create_sample_battlefield_json_data()
        
        # 执行一些处理以生成统计数据
        print("执行性能测试...")
        for data in sample_data[:2]:  # 处理前两条数据
            pipeline.process_single_battlefield_data(data)
        
        # 获取性能统计
        stats = pipeline.get_pipeline_stats()
        
        print(f"📈 Pipeline性能统计:")
        print(f"  系统运行时间: {stats['pipeline_uptime']:.2f}秒")
        
        pipeline_stats = stats['pipeline_stats']
        print(f"  总Pipeline数: {pipeline_stats['total_pipelines']}")
        print(f"  成功Pipeline数: {pipeline_stats['successful_pipelines']}")
        print(f"  失败Pipeline数: {pipeline_stats['failed_pipelines']}")
        print(f"  成功率: {pipeline_stats['success_rate']}%")
        print(f"  平均处理时间: {pipeline_stats['average_processing_time']:.2f}ms")
        print(f"  处理速度: {pipeline_stats['pipelines_per_second']:.2f} 个/秒")
        
        # 组件统计
        component_stats = stats['component_stats']
        
        print(f"\n📊 组件性能统计:")
        data_processor_stats = component_stats['data_processor']
        print(f"  数据处理器:")
        print(f"    处理数据量: {data_processor_stats['total_processed']}")
        print(f"    查询成功率: {data_processor_stats['success_rate']}%")
        print(f"    处理速度: {data_processor_stats['processing_rate']:.2f} 个/秒")
        
        llm_stats = component_stats['llm_service']
        print(f"  LLM服务:")
        print(f"    总请求数: {llm_stats['total_requests']}")
        print(f"    成功率: {llm_stats['success_rate']}%")
        print(f"    平均Token消耗: {llm_stats['average_tokens_per_request']:.0f}")
        print(f"    请求速度: {llm_stats['requests_per_second']:.2f} 个/秒")
        
        print(f"\n💚 系统健康状态: {stats['system_health']}")
        
    except Exception as e:
        print(f"❌ 性能监控演示失败: {str(e)}")


def demo_llm_prompt_preview():
    """演示LLM提示词预览"""
    print("\n=== LLM提示词预览演示 ===\n")
    
    try:
        pipeline = BattlefieldAIPipeline()
        sample_data = create_sample_battlefield_json_data()[0]  # 使用第一个示例
        
        print("生成LLM提示词预览...")
        
        # 只执行到提示词构建阶段
        processed_data = pipeline.data_processor.process_battlefield_stream(sample_data)
        
        if processed_data["status"] == "success":
            prompt_data = pipeline.prompt_builder.build_complete_prompt(processed_data)
            
            print(f"📝 提示词信息:")
            metadata = prompt_data["prompt_metadata"]
            print(f"  战场ID: {metadata['battlefield_id']}")
            print(f"  武器名称: {metadata['weapon_name']}")
            print(f"  威胁等级: {metadata['threat_level']}")
            print(f"  提示词长度: {metadata['prompt_length']} 字符")
            print(f"  包含武器知识: {metadata['has_weapon_knowledge']}")
            print(f"  我方部队数: {metadata['friendly_units_count']}")
            
            print(f"\n📋 系统提示词预览 (前200字符):")
            print(f"  {prompt_data['system_prompt'][:200]}...")
            
            print(f"\n📋 用户提示词预览 (前500字符):")
            print(f"  {prompt_data['user_prompt'][:500]}...")
            
        else:
            print(f"❌ 数据处理失败: {processed_data['error_message']}")
            
    except Exception as e:
        print(f"❌ 提示词预览演示失败: {str(e)}")


def main():
    """主函数"""
    print("🎯 战场AI分析Pipeline完整演示")
    print("=" * 60)
    print("系统功能:")
    print("  1. 流式JSON数据处理")
    print("  2. MySQL武器知识库查询")
    print("  3. LLM提示词智能构建")
    print("  4. 大模型战术分析")
    print("  5. 完整Pipeline自动化")
    print("=" * 60)
    
    # 单条数据Pipeline分析
    demo_single_pipeline_analysis()
    
    # 批量Pipeline处理
    demo_batch_pipeline_processing()
    
    # Pipeline性能监控
    demo_pipeline_performance()
    
    # LLM提示词预览
    demo_llm_prompt_preview()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成")
    print("\n💡 系统特点:")
    print("  ✅ 完整的AI分析Pipeline")
    print("  ✅ 流式JSON数据处理")
    print("  ✅ 智能武器知识库查询")
    print("  ✅ 动态LLM提示词构建")
    print("  ✅ 实时战术分析生成")
    print("  ✅ 批量处理支持")
    print("  ✅ 完整的性能监控")
    print("  ✅ 多LLM提供商支持")


if __name__ == "__main__":
    main()
