"""
战场武器识别与分析系统演示
完整展示从图像识别到战场分析的整个流程
"""
import json
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.battlefield_system import BattlefieldSystem


def create_sample_weapon_recognition_data():
    """创建示例武器识别数据"""
    
    # 场景1: 高威胁 - 主战坦克编队
    high_threat_scenario = {
        "image_id": "battlefield_001",
        "timestamp": time.time(),
        "battlefield_info": {
            "location": "35.1234, 118.5678",
            "weather": "晴朗",
            "visibility": "良好",
            "terrain": "开阔地"
        },
        "detected_weapons": [
            {
                "weapon_id": "weapon_001",
                "weapon_type": "主战坦克",
                "weapon_model": "T-90主战坦克",
                "confidence": 0.95,
                "position": {
                    "bbox": [100, 100, 300, 200],
                    "coordinates": "35.1235, 118.5679"
                },
                "status": "moving",
                "direction": "northeast"
            },
            {
                "weapon_id": "weapon_002",
                "weapon_type": "主战坦克", 
                "weapon_model": "T-72主战坦克",
                "confidence": 0.88,
                "position": {
                    "bbox": [350, 150, 550, 250],
                    "coordinates": "35.1236, 118.5680"
                },
                "status": "moving",
                "direction": "northeast"
            }
        ]
    }
    
    # 场景2: 中等威胁 - 装甲车队
    medium_threat_scenario = {
        "image_id": "battlefield_002",
        "timestamp": time.time(),
        "battlefield_info": {
            "location": "35.2345, 118.6789",
            "weather": "多云",
            "visibility": "一般",
            "terrain": "丘陵地"
        },
        "detected_weapons": [
            {
                "weapon_id": "weapon_003",
                "weapon_type": "装甲车",
                "weapon_model": "BTR-80装甲运兵车",
                "confidence": 0.92,
                "position": {
                    "bbox": [200, 200, 400, 300],
                    "coordinates": "35.2346, 118.6790"
                },
                "status": "stationary",
                "direction": "south"
            }
        ]
    }
    
    # 场景3: 无威胁 - 未识别到武器
    no_threat_scenario = {
        "image_id": "battlefield_003",
        "timestamp": time.time(),
        "battlefield_info": {
            "location": "35.3456, 118.7890",
            "weather": "晴朗",
            "visibility": "优秀",
            "terrain": "平原"
        },
        "detected_weapons": []
    }
    
    return [high_threat_scenario, medium_threat_scenario, no_threat_scenario]


def demo_single_battlefield_analysis():
    """演示单个战场分析"""
    print("=== 单个战场分析演示 ===\n")
    
    try:
        # 初始化战场系统
        battlefield_system = BattlefieldSystem()
        
        # 测试系统连接
        connectivity = battlefield_system.test_system_connectivity()
        print(f"系统连接状态: {connectivity['overall_status']}")
        
        if connectivity['overall_status'] != "正常":
            print("系统连接异常，请检查配置")
            return
        
        # 获取示例数据
        scenarios = create_sample_weapon_recognition_data()
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n--- 场景 {i}: {scenario['image_id']} ---")
            print(f"战场位置: {scenario['battlefield_info']['location']}")
            print(f"检测武器数: {len(scenario['detected_weapons'])}")
            
            # 处理战场图像
            result = battlefield_system.process_battlefield_image(scenario)
            
            if result["status"] == "mission_completed":
                print(f"✓ 任务完成")
                print(f"  任务ID: {result['mission_id']}")
                print(f"  总处理时间: {result['total_processing_time']}ms")
                
                # 显示任务摘要
                summary = result["mission_summary"]
                print(f"  威胁等级: {summary['threat_level']}")
                print(f"  威胁描述: {summary['threat_description']}")
                print(f"  建议行动: {summary['recommended_action']}")
                print(f"  优先级: {summary['priority']}")
                print(f"  成功概率: {summary['success_probability']}")
                
                # 显示关键行动
                if summary['key_actions']:
                    print(f"  关键行动:")
                    for action in summary['key_actions']:
                        print(f"    - {action}")
                
                # 显示武器情报
                weapon_data = result["weapon_intelligence"]["weapon_data"]
                if weapon_data and weapon_data["found_weapons"]:
                    print(f"  识别武器:")
                    for weapon_name in weapon_data["found_weapons"].keys():
                        print(f"    - {weapon_name}")
                
            else:
                print(f"✗ 任务失败: {result['error_message']}")
            
            print()
        
    except Exception as e:
        print(f"✗ 演示失败: {str(e)}")


def demo_detailed_analysis_output():
    """演示详细分析输出"""
    print("=== 详细分析输出演示 ===\n")
    
    try:
        battlefield_system = BattlefieldSystem()
        
        # 使用高威胁场景
        scenarios = create_sample_weapon_recognition_data()
        high_threat_scenario = scenarios[0]
        
        print("处理高威胁场景...")
        result = battlefield_system.process_battlefield_image(high_threat_scenario)
        
        if result["status"] == "mission_completed":
            print("=== 完整分析结果 ===")
            
            # 武器情报部分
            print("\n1. 武器情报:")
            weapon_intel = result["weapon_intelligence"]
            print(f"   检测武器数: {weapon_intel['detected_weapons_count']}")
            print(f"   识别型号数: {weapon_intel['identified_models_count']}")
            
            if weapon_intel["weapon_data"]["found_weapons"]:
                for weapon_name, weapon_info in weapon_intel["weapon_data"]["found_weapons"].items():
                    print(f"\n   武器: {weapon_name}")
                    print(f"     类别: {weapon_info['basic_info']['category']}")
                    print(f"     威胁等级: {weapon_info['combat_capabilities']['threat_level']}")
                    print(f"     对抗措施: {', '.join(weapon_info['countermeasures'][:2])}")
            
            # 战场评估部分
            print("\n2. 战场评估:")
            assessment = result["battlefield_analysis"]["battlefield_assessment"]
            print(f"   威胁等级: {assessment['threat_level']}")
            print(f"   敌方力量: {assessment['enemy_strength']}")
            print(f"   战术态势: {assessment['tactical_situation']}")
            
            if assessment["immediate_threats"]:
                print(f"   直接威胁:")
                for threat in assessment["immediate_threats"]:
                    print(f"     - {threat['weapon']}: {threat['threat_level']} ({threat['reason']})")
            
            # 战术建议部分
            print("\n3. 战术建议:")
            recommendations = result["battlefield_analysis"]["tactical_recommendations"]
            print(f"   立即行动:")
            for action in recommendations["immediate_actions"][:3]:
                print(f"     - {action}")
            
            print(f"   战略建议:")
            for suggestion in recommendations["strategic_suggestions"][:3]:
                print(f"     - {suggestion}")
            
            print(f"   资源需求:")
            for resource in recommendations["resource_requirements"][:3]:
                print(f"     - {resource}")
            
            # 风险评估部分
            print("\n4. 风险评估:")
            risk = result["battlefield_analysis"]["risk_assessment"]
            print(f"   成功概率: {risk['success_probability']}")
            print(f"   伤亡估计: {risk['casualty_estimate']}")
            print(f"   任务时长: {risk['mission_duration']}")
            
            # 替代策略部分
            print("\n5. 替代策略:")
            strategies = result["battlefield_analysis"]["alternative_strategies"]
            for strategy in strategies[:2]:
                print(f"   策略: {strategy['strategy']}")
                print(f"     描述: {strategy['description']}")
                print(f"     成功率: {strategy['success_rate']}")
                print(f"     优点: {', '.join(strategy['pros'][:2])}")
                print(f"     缺点: {', '.join(strategy['cons'][:2])}")
                print()
        
    except Exception as e:
        print(f"✗ 详细分析演示失败: {str(e)}")


def demo_system_performance():
    """演示系统性能"""
    print("=== 系统性能演示 ===\n")
    
    try:
        battlefield_system = BattlefieldSystem()
        scenarios = create_sample_weapon_recognition_data()
        
        print("批量处理性能测试...")
        
        start_time = time.time()
        results = []
        
        # 批量处理多个场景
        for scenario in scenarios:
            result = battlefield_system.process_battlefield_image(scenario)
            results.append(result)
        
        total_time = time.time() - start_time
        
        print(f"批量处理完成:")
        print(f"  处理场景数: {len(scenarios)}")
        print(f"  总耗时: {total_time:.2f}秒")
        print(f"  平均处理时间: {total_time/len(scenarios):.2f}秒/场景")
        
        # 统计结果
        successful = sum(1 for r in results if r["status"] == "mission_completed")
        failed = len(results) - successful
        
        print(f"  成功处理: {successful}")
        print(f"  处理失败: {failed}")
        print(f"  成功率: {successful/len(results)*100:.1f}%")
        
        # 显示系统状态
        print(f"\n系统状态:")
        status = battlefield_system.get_system_status()
        print(f"  系统运行时间: {status['system_uptime']:.2f}秒")
        print(f"  总任务数: {status['system_stats']['total_missions']}")
        print(f"  系统成功率: {status['system_stats']['success_rate']}%")
        print(f"  处理速度: {status['system_stats']['missions_per_second']:.2f} 任务/秒")
        print(f"  系统健康: {status['system_health']}")
        
    except Exception as e:
        print(f"✗ 性能演示失败: {str(e)}")


def demo_json_output_format():
    """演示JSON输出格式"""
    print("=== JSON输出格式演示 ===\n")
    
    try:
        battlefield_system = BattlefieldSystem()
        scenarios = create_sample_weapon_recognition_data()
        
        # 处理一个场景并输出完整JSON
        result = battlefield_system.process_battlefield_image(scenarios[0])
        
        print("完整JSON输出格式:")
        print(json.dumps(result, ensure_ascii=False, indent=2))
        
    except Exception as e:
        print(f"✗ JSON格式演示失败: {str(e)}")


def main():
    """主函数"""
    print("战场武器识别与分析系统演示")
    print("=" * 50)
    
    # 单个战场分析演示
    demo_single_battlefield_analysis()
    
    # 详细分析输出演示
    demo_detailed_analysis_output()
    
    # 系统性能演示
    demo_system_performance()
    
    # JSON输出格式演示（可选，输出较长）
    # demo_json_output_format()
    
    print("\n" + "=" * 50)
    print("演示完成")
    print("\n系统特点:")
    print("✓ 完整的武器识别到战场分析流程")
    print("✓ 智能威胁等级评估")
    print("✓ 详细的战术建议和风险评估")
    print("✓ 多种替代策略分析")
    print("✓ 实时性能监控")


if __name__ == "__main__":
    main()
