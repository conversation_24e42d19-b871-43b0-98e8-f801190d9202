"""
流式查询API示例 - 展示如何在实际应用中集成流式查询
"""
import json
import time
from typing import Dict, Any, List
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.stream_query_service import StreamQueryService


class ImageRecognitionAPI:
    """图像识别API模拟类"""
    
    def __init__(self):
        self.stream_service = StreamQueryService()
    
    def process_single_image(self, image_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个图像的识别和查询
        
        Args:
            image_data: 图像数据和识别结果
            
        Returns:
            处理结果
        """
        try:
            # 模拟图像识别过程
            recognition_result = self._simulate_image_recognition(image_data)
            
            # 执行流式查询
            query_result = self.stream_service.stream_query(recognition_result)
            
            # 构建API响应
            api_response = {
                "success": True,
                "image_id": query_result["image_id"],
                "processing_time_ms": query_result["processing_time"],
                "recognition": {
                    "detected_count": query_result["detected_count"],
                    "confidence": recognition_result.get("confidence", 0.0)
                },
                "database_query": {
                    "status": query_result["status"],
                    "found_count": query_result["query_results"]["total_found"] if query_result["query_results"] else 0,
                    "results": query_result["query_results"]["found"] if query_result["query_results"] else {}
                },
                "timestamp": time.time()
            }
            
            return api_response
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def process_image_batch(self, image_batch: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量处理图像
        
        Args:
            image_batch: 图像批次
            
        Returns:
            批量处理结果
        """
        try:
            # 模拟批量图像识别
            recognition_batch = [self._simulate_image_recognition(img) for img in image_batch]
            
            # 执行批量流式查询
            query_results = self.stream_service.batch_stream_query(recognition_batch)
            
            # 构建批量API响应
            api_response = {
                "success": True,
                "batch_size": len(image_batch),
                "processed_count": len(query_results),
                "total_found": sum(r["query_results"]["total_found"] for r in query_results if r.get("query_results")),
                "results": query_results,
                "stats": self.stream_service.get_stream_stats(),
                "timestamp": time.time()
            }
            
            return api_response
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def _simulate_image_recognition(self, image_data: Dict[str, Any]) -> Dict[str, Any]:
        """模拟图像识别过程"""
        
        # 从输入数据中提取信息
        image_id = image_data.get("image_id", f"img_{int(time.time() * 1000)}")
        objects = image_data.get("objects", [])
        
        # 模拟识别结果
        recognition_result = {
            "image_id": image_id,
            "timestamp": time.time(),
            "status": "success" if objects else "no_detection",
            "confidence": max([obj.get("confidence", 0.8) for obj in objects]) if objects else 0.0,
            "detected_objects": objects
        }
        
        return recognition_result
    
    def get_api_stats(self) -> Dict[str, Any]:
        """获取API统计信息"""
        return {
            "stream_stats": self.stream_service.get_stream_stats(),
            "api_version": "1.0.0",
            "uptime": time.time()
        }


def demo_single_image_api():
    """演示单个图像API"""
    print("=== 单个图像API演示 ===\n")
    
    api = ImageRecognitionAPI()
    
    # 测试用例1: 识别到飞机
    test_image_1 = {
        "image_id": "api_test_001",
        "image_path": "/path/to/aircraft_image.jpg",
        "objects": [
            {
                "name": "歼-20 战斗机",
                "confidence": 0.95,
                "bbox": [100, 100, 300, 200]
            }
        ]
    }
    
    print("测试1: 识别到单个飞机")
    result1 = api.process_single_image(test_image_1)
    print(f"   成功: {result1['success']}")
    print(f"   处理时间: {result1.get('processing_time_ms', 0)}ms")
    print(f"   找到记录: {result1.get('database_query', {}).get('found_count', 0)}")
    
    # 测试用例2: 识别到多个对象
    test_image_2 = {
        "image_id": "api_test_002",
        "image_path": "/path/to/multi_aircraft.jpg",
        "objects": [
            {"name": "歼-20 战斗机", "confidence": 0.92},
            {"name": "F-35 闪电 II", "confidence": 0.88},
            {"name": "不存在的飞机", "confidence": 0.75}
        ]
    }
    
    print("\n测试2: 识别到多个对象")
    result2 = api.process_single_image(test_image_2)
    print(f"   成功: {result2['success']}")
    print(f"   识别数量: {result2.get('recognition', {}).get('detected_count', 0)}")
    print(f"   找到记录: {result2.get('database_query', {}).get('found_count', 0)}")
    
    # 测试用例3: 空结果
    test_image_3 = {
        "image_id": "api_test_003",
        "image_path": "/path/to/empty_image.jpg",
        "objects": []
    }
    
    print("\n测试3: 空识别结果")
    result3 = api.process_single_image(test_image_3)
    print(f"   成功: {result3['success']}")
    print(f"   识别数量: {result3.get('recognition', {}).get('detected_count', 0)}")


def demo_batch_image_api():
    """演示批量图像API"""
    print("\n=== 批量图像API演示 ===\n")
    
    api = ImageRecognitionAPI()
    
    # 创建批量测试数据
    image_batch = [
        {
            "image_id": "batch_001",
            "objects": [{"name": "歼-20 战斗机", "confidence": 0.95}]
        },
        {
            "image_id": "batch_002", 
            "objects": [{"name": "F-35 闪电 II", "confidence": 0.88}]
        },
        {
            "image_id": "batch_003",
            "objects": []  # 空结果
        },
        {
            "image_id": "batch_004",
            "objects": [
                {"name": "歼-20 战斗机", "confidence": 0.90},
                {"name": "不存在的飞机", "confidence": 0.70}
            ]
        }
    ]
    
    print(f"批量处理 {len(image_batch)} 个图像...")
    
    batch_result = api.process_image_batch(image_batch)
    
    print(f"   成功: {batch_result['success']}")
    print(f"   批次大小: {batch_result.get('batch_size', 0)}")
    print(f"   处理数量: {batch_result.get('processed_count', 0)}")
    print(f"   总找到记录: {batch_result.get('total_found', 0)}")
    
    # 显示统计信息
    stats = batch_result.get('stats', {})
    print(f"\n批量处理统计:")
    print(f"   总请求数: {stats.get('total_requests', 0)}")
    print(f"   成功查询: {stats.get('successful_queries', 0)}")
    print(f"   成功率: {stats.get('success_rate', 0)}%")


def demo_real_time_stream():
    """演示实时流处理"""
    print("\n=== 实时流处理演示 ===\n")
    
    api = ImageRecognitionAPI()
    
    print("模拟实时图像流处理...")
    
    # 模拟实时数据流
    for i in range(3):
        # 动态生成图像数据
        image_data = {
            "image_id": f"stream_{i+1}",
            "timestamp": time.time(),
            "objects": [
                {
                    "name": "歼-20 战斗机" if i % 2 == 0 else "F-35 闪电 II",
                    "confidence": 0.85 + (i * 0.05)
                }
            ]
        }
        
        print(f"\n处理实时图像 {i+1}:")
        
        # 处理图像
        result = api.process_single_image(image_data)
        
        print(f"   图像ID: {result.get('image_id', 'unknown')}")
        print(f"   处理时间: {result.get('processing_time_ms', 0)}ms")
        print(f"   查询状态: {result.get('database_query', {}).get('status', 'unknown')}")
        
        # 模拟实时间隔
        time.sleep(0.5)
    
    # 显示最终统计
    print(f"\n实时流处理统计:")
    stats = api.get_api_stats()
    stream_stats = stats['stream_stats']
    print(f"   处理速度: {stream_stats.get('requests_per_second', 0)} 请求/秒")
    print(f"   成功率: {stream_stats.get('success_rate', 0)}%")


def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===\n")
    
    api = ImageRecognitionAPI()
    
    # 测试错误情况
    error_cases = [
        {
            "name": "无效图像数据",
            "data": None
        },
        {
            "name": "缺少必要字段",
            "data": {"invalid": "data"}
        }
    ]
    
    for case in error_cases:
        print(f"测试: {case['name']}")
        try:
            result = api.process_single_image(case['data'])
            print(f"   成功: {result['success']}")
            if not result['success']:
                print(f"   错误: {result.get('error', 'unknown')}")
        except Exception as e:
            print(f"   异常: {str(e)}")


def main():
    """主函数"""
    print("流式查询API演示")
    print("=" * 50)
    
    # 单个图像API演示
    demo_single_image_api()
    
    # 批量图像API演示
    demo_batch_image_api()
    
    # 实时流处理演示
    demo_real_time_stream()
    
    # 错误处理演示
    demo_error_handling()
    
    print("\n" + "=" * 50)
    print("API演示结束")
    print("\nAPI特点:")
    print("1. 统一的请求/响应格式")
    print("2. 支持单个和批量处理")
    print("3. 实时性能监控")
    print("4. 完善的错误处理")
    print("5. 标准化的状态码")


if __name__ == "__main__":
    main()
