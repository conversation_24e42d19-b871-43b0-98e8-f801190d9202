{"stream_query": {"confidence_threshold": 0.5, "max_batch_size": 100, "timeout_seconds": 30, "enable_stats": true, "log_level": "INFO"}, "image_recognition": {"supported_formats": ["jpg", "jpeg", "png", "bmp"], "max_objects_per_image": 50, "min_bbox_size": 10}, "performance": {"connection_pool_size": 10, "query_timeout": 5, "enable_caching": false, "cache_ttl_seconds": 300}, "monitoring": {"enable_metrics": true, "metrics_interval_seconds": 60, "alert_error_rate_threshold": 0.1, "alert_response_time_threshold": 1000}}