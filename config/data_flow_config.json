{"data_flow_controller": {"buffer_size": 100, "ai_analysis_interval": 30, "frontend_update_interval": 1, "change_threshold": 0.3, "max_concurrent_ai_tasks": 2, "api_url": "http://localhost:8010", "priority_settings": {"critical_change_threshold": 0.7, "high_threat_ratio": 0.5, "medium_change_threshold": 0.3}, "performance_settings": {"ai_request_timeout": 60, "frontend_request_timeout": 5, "max_buffer_age_seconds": 300}}, "mqtt_integration": {"log_level": "INFO", "stats_interval": 10, "enable_detailed_logging": false}}