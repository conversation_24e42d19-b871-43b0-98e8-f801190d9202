# 系统启动说明

## 🎯 核心程序（生产环境）

### 1. 启动FastAPI服务
```bash
python main_fastapi.py
```
- **作用**：提供AI分析和前端API服务
- **端口**：8000
- **API端点**：
  - `/battlefield/analyze` - 接收战场数据进行AI分析
  - `/threed/units` - 接收3D显示数据并推送给前端

### 2. 启动MQTT数据接收器
```bash
python mqtt_data_receiver.py
```
- **作用**：接收MQTT数据，通过数据流控制器处理后发送给FastAPI
- **数据流程**：
  ```
  MQTT数据(15Hz) → 数据流控制器 → FastAPI服务
                      ↓
                  智能调度：
                  - AI分析(30s间隔)
                  - 前端更新(1s间隔)
  ```

## 📋 测试和演示文件（非生产环境）

### 1. demo_data_flow.py
- **作用**：演示数据流控制器的工作原理
- **用途**：
  - 理解优先级处理机制
  - 验证缓冲区管理
  - 学习系统工作原理
- **不是生产环境使用的**

### 2. test_high_frequency_data.py
- **作用**：测试15Hz高频数据处理能力
- **用途**：性能测试和验证

## 🚀 正确的启动顺序

### 生产环境启动：
```bash
# 1. 启动FastAPI服务（后台运行）
python main_fastapi.py &

# 2. 启动MQTT接收器
python mqtt_data_receiver.py
```

### 开发测试：
```bash
# 1. 启动FastAPI服务
python main_fastapi.py

# 2. 新终端启动MQTT接收器
python mqtt_data_receiver.py

# 3. 可选：运行演示程序了解工作原理
python demo_data_flow.py
```

## 📊 数据流程图

```
实际MQTT数据源
    ↓
mqtt_data_receiver.py
    ├── 接收MQTT数据(15Hz)
    ├── 数据流控制器处理
    │   ├── 缓冲队列
    │   ├── 优先级评估
    │   └── 智能调度
    └── 发送到FastAPI
        ↓
main_fastapi.py
    ├── /battlefield/analyze → AI分析
    └── /threed/units → 前端3D显示
```

## ⚙️ 配置文件

### MQTT配置 (config/mqtt_config.json)
```json
{
  "mqtt": {
    "server_address": "tcp://************:1883",
    "username": "zkhd",
    "password": "zk45619",
    "topic": "sys/esdk/status/8UUXN2Q00A01GE"
  }
}
```

### 数据流控制器配置 (config/data_flow_config.json)
```json
{
  "data_flow_controller": {
    "buffer_size": 100,
    "ai_analysis_interval": 30,
    "frontend_update_interval": 1,
    "max_concurrent_ai_tasks": 2
  }
}
```

## 🔍 监控和调试

### 查看日志
- MQTT接收器会显示接收统计
- 数据流控制器会显示处理统计
- FastAPI会显示API调用情况

### 性能监控
```bash
# 查看MQTT接收统计
# 在mqtt_data_receiver.py运行时会每10秒显示统计信息

# 查看FastAPI访问日志
# 在main_fastapi.py运行时会显示API调用情况
```

## ❓ 常见问题

### Q: demo_data_flow.py的作用是什么？
A: 这是一个**演示脚本**，用于展示数据流控制器的工作原理，帮助理解系统如何处理高频数据。不是生产环境使用的。

### Q: 为什么需要数据流控制器？
A: 因为MQTT每秒15次数据，但大模型需要30秒分析一次，前端也无法每秒渲染15次。数据流控制器解决了这个速率不匹配问题。

### Q: 如何验证系统正常工作？
A: 
1. 启动main_fastapi.py，访问 http://localhost:8000/docs 查看API文档
2. 启动mqtt_data_receiver.py，观察是否正常接收MQTT数据
3. 查看日志中的统计信息，确认数据正常流转

### Q: 如何调整处理频率？
A: 修改 `config/data_flow_config.json` 中的参数：
- `ai_analysis_interval`: AI分析间隔
- `frontend_update_interval`: 前端更新间隔
