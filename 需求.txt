分析该工程文件：完成以下需求
1.编写MySQL连接工具，进行数据的流式查询。查询字段来源于流式JSON数据,格式如下：
{ID：
武器名称：
武器类型：
精度：
纬度：
高度：
属方：
} 通过武器名称和武器类型去MySQl知识库中查询，该武器的性能以及克制武器等信息，直接将匹配字段全字段返回即可。
2. 进行llm提示词的构建，（包括敌方信息，我方信息，任务），用大模型给出击败敌方的方案
3.组成pipeline。进行执行
因为查询的战场信息是流式动态的，你需要考虑大模型如何接收到战场信息后进行自动分析。

帮我写一份使用文档。我需要知道以下详细信息。
1. 我的流式Json数据通过哪个文件中的哪个方法传入，传入之后，去如何处理？调用的哪个方法进行数据库检索。我该如何模拟流式数据去测试该功能。
2.战场信息，和我方信息是如何组成提示词的？以及提示词任务是如何设定的？
3.如何修改配置文件去配置我的llm大模型
4.如何去运行测试该系统，并看到每一个的输出结果。


现在我需要将battlefield_simulator.py模拟数据发送程序替换为真实的数据来源。保留现在
的代码不动，增加接入数据源的程序，并对接收到的程序进行处理。
帮我写一个MQTT客户端并对接收到的数据进行处理。
1.我的数据接入通过MQTT程序进行数据接入，MQTT数据接入信息如下，通过配置文件实现：
- 协议：mqtt
- 登录信息
    "server_address": "tcp://************:1883",  非ssl
    "username": "zkhd",
    "password": "zk45619"

topic:  sys/esdk/status/8UUXN2Q00A01GE
2.接收到到的数据格式如下：
{
  "code" : 0,
  "id" : "-6",
  "seq":"26543322322",
  "time":"2025-9-12 12:34:21",
  "objectList" : [ {
    "class_name" : "camera",
    "confidence":0.67,
    "height" : 32.0,
    "id" : 124745,
    "loc_height" : 557.7999877929688,
    "loc_latitude" : 40.35440069941853,
    "loc_longitude" : 115.91258306859129,
    "speed" : 0,
    "url" : "ocrByBox/2025-09/02--19-15-38-crop_124745-887268092.jpg",
    "width" : 54.2012939453125
  }, {
    "class_name" : "camera",
    "height" : -900.4287109375,
    "id" : 112287,
    "loc_height" : 557.7999877929688,
    "loc_latitude" : 40.35520917947602,
    "loc_longitude" : 115.90932933903598,
    "speed" : 0,
    "url" : "ocrByBox/2025-09/02--19-15-26-crop_112287-1314559716.jpg",
    "width" : -1177.**********
  }]
}
  你需要帮我处理成这种数据格式：
  BattlefieldUnit(
                id="ENEMY_001",
                name="T-90主战坦克",
                type="主战坦克",
                side="敌方",
                position=Position(116.3974, 39.9042, 45.2),
                status=UnitStatus(100.0, 85.0, 90.0, True),
                threat_level="高",
                confidence=0.95,
                last_seen=datetime.now().isoformat(),
                speed=25.0,  # km/h
                heading=45.0
            )
接收到的数据与BattlefieldUnit对应关系：
objectList中的id 对应BattlefieldUnit中的id
objectList中的class_name 对应BattlefieldUnit中的name
BattlefieldUnit中的type 暂时等于 class_name
BattlefieldUnit中的side 暂时等于 "敌方"
BattlefieldUnit中的position 对应 objectList中的loc_longitude, loc_latitude, loc_height 
BattlefieldUnit中的status信息暂定为UnitStatus(100.0, 85.0, 90.0, True)
BattlefieldUnit中的threat_level 暂定为"中等"
BattlefieldUnit中的confidence 对应objectList中的confidence
BattlefieldUnit中的last_seen对应接收到数据的time字段。
BattlefieldUnit中的speed 对应objectList中的speed
BattlefieldUnit中的heading 暂定为0.0


我现在遇到这样一个问题，该如何处理？
我的mqtt_data_receiver.py，MQTT每秒钟最多能接收到15次数据，也就是15次战场信息，这个速度还没办法修改，对于
大模型处理可能需要30秒左右才能分析完一次态势，该如何处理接收到数据？另外对于前端3维显示1秒钟也无法渲染15张图片

因为现在mqtt服务端还无法对接，我需要在本地写一个mqtt服务端去模拟真实的数据发送，发送格式如下，每秒最多发送15张图片
{
  "code" : 0,
  "id" : "-6",
  "seq":"26543322322",
  "time":"2025-9-12 12:34:21",
  "objectList" : [ {
    "class_name" : "camera",
    "confidence":0.67,
    "height" : 32.0,
    "id" : 124745,
    "loc_height" : 557.7999877929688,
    "loc_latitude" : 40.35440069941853,
    "loc_longitude" : 115.91258306859129,
    "speed" : 0,
    "url" : "ocrByBox/2025-09/02--19-15-38-crop_124745-887268092.jpg",
    "width" : 54.2012939453125
  }, {
    "class_name" : "camera",
    "height" : -900.4287109375,
    "id" : 112287,
    "loc_height" : 557.7999877929688,
    "loc_latitude" : 40.35520917947602,
    "loc_longitude" : 115.90932933903598,
    "speed" : 0,
    "url" : "ocrByBox/2025-09/02--19-15-26-crop_112287-1314559716.jpg",
    "width" : -1177.**********
  }]
}

我该如何测试 mptt_data_receiver.py,能够正常接收数据，并进行处理，创建，发送战场数据包等功能均正常，注意我main_fastapi工作正常，但目前环境还不能运行。

这个结构需要修改，因为我知道我方挂载了哪些弹型，是一个list结构，我认为弹药装态可以
没有，为了保持统一，地方单位也采用更新后的list结构
@dataclass
class UnitStatus:
    """单位状态"""
    health: float      # 生命值 (0-100)
    ammo: float       # 弹药状态 (0-100)
    fuel: float       # 燃料状态 (0-100)
    operational: bool  # 是否可操作

我把Position、UnitStatus、BattlefieldUnit三个类复制到mqtt_data_receiver.py中，这样不依赖其他文件
