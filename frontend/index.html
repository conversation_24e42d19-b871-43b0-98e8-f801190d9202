<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战场态势分析系统 - WebSocket实时版</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🎯 战场态势分析系统 (WebSocket实时版)</h1>
            <div class="status-bar">
                <span id="connection-status" class="status-indicator">🔴 未连接</span>
                <span id="last-update">最后更新: 无</span>
                <button id="connect-btn" onclick="connectWebSocket()">连接WebSocket</button>
                <button id="disconnect-btn" onclick="disconnectWebSocket()" disabled>断开连接</button>
            </div>
        </header>

        <main>
            <div class="dashboard">
                <!-- 系统状态面板 -->
                <div class="panel system-status">
                    <h2>📊 系统状态</h2>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="label">API状态:</span>
                            <span id="api-status" class="value">检查中...</span>
                        </div>
                        <div class="status-item">
                            <span class="label">WebSocket:</span>
                            <span id="ws-status" class="value">未连接</span>
                        </div>
                        <div class="status-item">
                            <span class="label">连接数:</span>
                            <span id="connection-count" class="value">0</span>
                        </div>
                        <div class="status-item">
                            <span class="label">分析次数:</span>
                            <span id="analysis-count" class="value">0</span>
                        </div>
                        <div class="status-item">
                            <span class="label">处理时间:</span>
                            <span id="processing-time" class="value">-</span>
                        </div>
                        <div class="status-item">
                            <span class="label">最新分析ID:</span>
                            <span id="analysis-id" class="value">-</span>
                        </div>
                    </div>
                </div>

                <!-- 威胁评估面板 -->
                <div class="panel threat-assessment">
                    <h2>⚠️ 威胁评估</h2>
                    <div id="threat-content">
                        <p class="no-data">等待WebSocket实时数据...</p>
                    </div>
                </div>

                <!-- 战术建议面板 -->
                <div class="panel tactical-recommendation">
                    <h2>🎯 战术建议</h2>
                    <div id="tactical-content">
                        <p class="no-data">等待WebSocket实时数据...</p>
                    </div>
                </div>

                <!-- 实时消息面板 -->
                <div class="panel messages">
                    <h2>📡 实时消息</h2>
                    <div id="message-container">
                        <div class="message-entry">
                            <span class="timestamp">[系统启动]</span>
                            <span class="message">等待WebSocket连接...</span>
                        </div>
                    </div>
                    <button onclick="clearMessages()">清空消息</button>
                </div>

                <!-- 实时日志面板 -->
                <div class="panel logs">
                    <h2>📝 实时日志</h2>
                    <div id="log-container">
                        <div class="log-entry">
                            <span class="timestamp">[系统启动]</span>
                            <span class="message">WebSocket实时推送系统已加载</span>
                        </div>
                    </div>
                    <button onclick="clearLogs()">清空日志</button>
                </div>
            </div>
        </main>
    </div>

    <script src="app.js"></script>
</body>
</html>
