// WebSocket实时战场态势分析系统
class BattlefieldWebSocketClient {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectInterval = 3000;
        this.heartbeatInterval = null;
        this.analysisCount = 0;
        
        // API配置
        this.apiBaseUrl = 'http://localhost:8000';
        this.wsUrl = 'ws://localhost:8000/ws/battlefield';
        
        // 初始化
        this.init();
    }
    
    init() {
        console.log('🚀 初始化WebSocket客户端...');
        this.addLog('系统初始化', '正在启动WebSocket客户端...');
        
        // 检查API状态
        this.checkApiStatus();
        
        // 自动连接WebSocket
        setTimeout(() => {
            this.connectWebSocket();
        }, 1000);
    }
    
    async checkApiStatus() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            if (response.ok) {
                const data = await response.json();
                this.updateElement('api-status', '🟢 正常');
                this.addLog('API检查', 'API服务正常运行');
            } else {
                this.updateElement('api-status', '🔴 异常');
                this.addLog('API检查', 'API服务响应异常');
            }
        } catch (error) {
            this.updateElement('api-status', '🔴 离线');
            this.addLog('API检查', `API服务连接失败: ${error.message}`);
        }
    }
    
    connectWebSocket() {
        if (this.isConnected) {
            this.addLog('连接', 'WebSocket已经连接');
            return;
        }
        
        try {
            this.addLog('连接', `正在连接WebSocket: ${this.wsUrl}`);
            this.ws = new WebSocket(this.wsUrl);
            
            this.ws.onopen = (event) => {
                this.onWebSocketOpen(event);
            };
            
            this.ws.onmessage = (event) => {
                this.onWebSocketMessage(event);
            };
            
            this.ws.onclose = (event) => {
                this.onWebSocketClose(event);
            };
            
            this.ws.onerror = (event) => {
                this.onWebSocketError(event);
            };
            
        } catch (error) {
            this.addLog('连接错误', `WebSocket连接失败: ${error.message}`);
            this.updateConnectionStatus('🔴 连接失败');
        }
    }
    
    onWebSocketOpen(event) {
        console.log('✅ WebSocket连接已建立');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        this.updateConnectionStatus('🟢 已连接');
        this.updateElement('ws-status', '🟢 已连接');
        this.updateElement('connect-btn', '已连接');
        document.getElementById('connect-btn').disabled = true;
        document.getElementById('disconnect-btn').disabled = false;
        
        this.addLog('连接成功', 'WebSocket连接已建立');
        
        // 启动心跳
        this.startHeartbeat();
    }
    
    onWebSocketMessage(event) {
        try {
            const message = JSON.parse(event.data);
            console.log('📨 收到WebSocket消息:', message);
            
            this.addMessage(`[${message.type}]`, JSON.stringify(message, null, 2));
            
            switch (message.type) {
                case 'connection':
                    this.handleConnectionMessage(message);
                    break;
                case 'analysis_result':
                    this.handleAnalysisResult(message);
                    break;
                case 'latest_analysis':
                    this.handleLatestAnalysis(message);
                    break;
                case 'status':
                    this.handleStatusMessage(message);
                    break;
                case 'pong':
                    this.addLog('心跳', '收到服务器心跳响应');
                    break;
                default:
                    this.addLog('消息', `未知消息类型: ${message.type}`);
            }
            
        } catch (error) {
            console.error('❌ 解析WebSocket消息失败:', error);
            this.addLog('解析错误', `消息解析失败: ${error.message}`);
        }
    }
    
    handleConnectionMessage(message) {
        this.addLog('连接', message.message);
        if (message.connection_id) {
            this.updateElement('connection-count', message.connection_id);
        }
    }
    
    handleAnalysisResult(message) {
        console.log('🎯 收到新的分析结果:', message.data);
        this.analysisCount++;
        
        const data = message.data;
        
        // 更新系统状态
        this.updateElement('analysis-count', this.analysisCount);
        this.updateElement('processing-time', `${data.processing_time_ms}ms`);
        this.updateElement('analysis-id', data.analysis_id);
        this.updateElement('last-update', new Date().toLocaleTimeString());
        
        // 更新威胁评估
        this.updateThreatAssessment(data.threat_assessment);
        
        // 更新战术建议
        this.updateTacticalRecommendation(data.tactical_recommendation);
        
        this.addLog('分析结果', `收到新的分析结果 - ID: ${data.analysis_id}`);
    }
    
    handleLatestAnalysis(message) {
        console.log('📊 收到最新分析数据:', message.data);
        this.handleAnalysisResult(message);
    }
    
    handleStatusMessage(message) {
        const status = message.data;
        this.updateElement('connection-count', status.active_connections);
        this.addLog('状态更新', `活跃连接: ${status.active_connections}`);
    }
    
    updateThreatAssessment(threatData) {
        const container = document.getElementById('threat-content');
        
        if (!threatData) {
            container.innerHTML = '<p class="no-data">暂无威胁评估数据</p>';
            return;
        }
        
        let html = '<div class="threat-data">';
        
        if (threatData.整体威胁等级) {
            const level = threatData.整体威胁等级;
            const levelClass = this.getThreatLevelClass(level);
            html += `<div class="threat-level ${levelClass}">
                        <strong>整体威胁等级:</strong> ${level}
                     </div>`;
        }
        
        if (threatData.主要威胁) {
            html += `<div class="main-threat">
                        <strong>主要威胁:</strong> ${threatData.主要威胁}
                     </div>`;
        }
        
        if (threatData.威胁排序 && Array.isArray(threatData.威胁排序)) {
            html += '<div class="threat-ranking"><strong>威胁排序:</strong><ol>';
            threatData.威胁排序.forEach(threat => {
                html += `<li>${threat}</li>`;
            });
            html += '</ol></div>';
        }
        
        html += '</div>';
        container.innerHTML = html;
    }
    
    updateTacticalRecommendation(tacticalData) {
        const container = document.getElementById('tactical-content');
        
        if (!tacticalData) {
            container.innerHTML = '<p class="no-data">暂无战术建议数据</p>';
            return;
        }
        
        let html = '<div class="tactical-data">';
        
        const recommendations = tacticalData.战术建议 || tacticalData;
        
        if (recommendations.推荐策略) {
            html += `<div class="strategy">
                        <strong>推荐策略:</strong> ${recommendations.推荐策略}
                     </div>`;
        }
        
        if (recommendations.优先目标) {
            html += `<div class="priority-target">
                        <strong>优先目标:</strong> ${recommendations.优先目标}
                     </div>`;
        }
        
        if (recommendations.兵力部署) {
            html += `<div class="deployment">
                        <strong>兵力部署:</strong> ${recommendations.兵力部署}
                     </div>`;
        }
        
        if (recommendations.注意事项 && Array.isArray(recommendations.注意事项)) {
            html += '<div class="precautions"><strong>注意事项:</strong><ul>';
            recommendations.注意事项.forEach(item => {
                html += `<li>${item}</li>`;
            });
            html += '</ul></div>';
        }
        
        html += '</div>';
        container.innerHTML = html;
    }
    
    getThreatLevelClass(level) {
        switch (level) {
            case '极高': return 'critical';
            case '高': return 'high';
            case '中': return 'medium';
            case '低': return 'low';
            default: return 'unknown';
        }
    }
    
    startHeartbeat() {
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
                this.sendMessage({
                    type: 'ping',
                    timestamp: new Date().toISOString()
                });
            }
        }, 30000); // 每30秒发送一次心跳
    }
    
    stopHeartbeat() {
        if (this.heartbeatInterval) {
            clearInterval(this.heartbeatInterval);
            this.heartbeatInterval = null;
        }
    }
    
    sendMessage(message) {
        if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
        }
    }
    
    onWebSocketClose(event) {
        console.log('❌ WebSocket连接已关闭:', event);
        this.isConnected = false;
        this.stopHeartbeat();
        
        this.updateConnectionStatus('🔴 已断开');
        this.updateElement('ws-status', '🔴 已断开');
        document.getElementById('connect-btn').disabled = false;
        document.getElementById('disconnect-btn').disabled = true;
        
        this.addLog('连接断开', `WebSocket连接已关闭 (代码: ${event.code})`);
        
        // 自动重连
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            this.addLog('重连', `${this.reconnectInterval/1000}秒后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
            setTimeout(() => {
                this.connectWebSocket();
            }, this.reconnectInterval);
        } else {
            this.addLog('重连失败', '已达到最大重连次数，请手动重连');
        }
    }
    
    onWebSocketError(event) {
        console.error('❌ WebSocket错误:', event);
        this.addLog('连接错误', 'WebSocket连接发生错误');
    }
    
    disconnectWebSocket() {
        if (this.ws) {
            this.ws.close();
            this.addLog('手动断开', '用户主动断开WebSocket连接');
        }
    }
    
    updateConnectionStatus(status) {
        this.updateElement('connection-status', status);
    }
    
    updateElement(id, content) {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = content;
        }
    }
    
    addLog(type, message) {
        const container = document.getElementById('log-container');
        const timestamp = new Date().toLocaleTimeString();
        
        const logEntry = document.createElement('div');
        logEntry.className = 'log-entry';
        logEntry.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="type">[${type}]</span>
            <span class="message">${message}</span>
        `;
        
        container.appendChild(logEntry);
        container.scrollTop = container.scrollHeight;
        
        // 限制日志条数
        const logs = container.children;
        if (logs.length > 100) {
            container.removeChild(logs[0]);
        }
    }
    
    addMessage(type, content) {
        const container = document.getElementById('message-container');
        const timestamp = new Date().toLocaleTimeString();
        
        const messageEntry = document.createElement('div');
        messageEntry.className = 'message-entry';
        messageEntry.innerHTML = `
            <span class="timestamp">[${timestamp}]</span>
            <span class="type">${type}</span>
            <pre class="content">${content}</pre>
        `;
        
        container.appendChild(messageEntry);
        container.scrollTop = container.scrollHeight;
        
        // 限制消息条数
        const messages = container.children;
        if (messages.length > 50) {
            container.removeChild(messages[0]);
        }
    }
    
    getStatus() {
        this.sendMessage({
            type: 'get_status',
            timestamp: new Date().toISOString()
        });
    }
}

// 全局变量
let wsClient;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 页面加载完成，初始化WebSocket客户端...');
    wsClient = new BattlefieldWebSocketClient();
});

// 全局函数
function connectWebSocket() {
    if (wsClient) {
        wsClient.connectWebSocket();
    }
}

function disconnectWebSocket() {
    if (wsClient) {
        wsClient.disconnectWebSocket();
    }
}

function clearLogs() {
    const container = document.getElementById('log-container');
    container.innerHTML = '';
    wsClient.addLog('系统', '日志已清空');
}

function clearMessages() {
    const container = document.getElementById('message-container');
    container.innerHTML = '';
    wsClient.addLog('系统', '消息已清空');
}

function getStatus() {
    if (wsClient) {
        wsClient.getStatus();
    }
}
