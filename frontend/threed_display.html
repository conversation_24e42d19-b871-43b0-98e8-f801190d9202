<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>战场三维显示系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            padding: 20px;
            overflow-y: auto;
        }

        .main-display {
            flex: 1;
            position: relative;
            background: #000;
        }

        .status-panel {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-label {
            color: #B0C4DE;
        }

        .status-value {
            color: #FFD700;
            font-weight: bold;
        }

        .units-panel {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }

        .unit-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 8px;
            font-size: 0.9em;
        }

        .unit-enemy {
            border-left: 4px solid #FF4500;
        }

        .unit-friendly {
            border-left: 4px solid #32CD32;
        }

        .unit-name {
            font-weight: bold;
            color: #FFD700;
        }

        .unit-position {
            color: #87CEEB;
            font-size: 0.8em;
        }

        .unit-status {
            color: #B0C4DE;
            font-size: 0.8em;
        }

        .logs-panel {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 10px;
            height: 200px;
            overflow-y: auto;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
        }

        .log-timestamp {
            color: #87CEEB;
        }

        .log-message {
            color: #F0F8FF;
        }

        .map-container {
            width: 100%;
            height: 100%;
            position: relative;
            background: 
                radial-gradient(circle at 20% 20%, rgba(0, 255, 0, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(255, 0, 0, 0.1) 0%, transparent 50%),
                linear-gradient(45deg, #001122 0%, #002244 100%);
        }

        .unit-marker {
            position: absolute;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .unit-marker.enemy {
            background: rgba(255, 69, 0, 0.8);
            border-color: #FF4500;
            color: white;
        }

        .unit-marker.friendly {
            background: rgba(50, 205, 50, 0.8);
            border-color: #32CD32;
            color: white;
        }

        .unit-marker:hover {
            transform: scale(1.5);
            z-index: 1000;
        }

        .unit-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px;
            border-radius: 5px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1001;
            display: none;
        }

        .connection-status {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 14px;
        }

        .connected {
            color: #32CD32;
        }

        .disconnected {
            color: #FF4500;
        }

        button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 12px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        button:disabled {
            background: #666;
            cursor: not-allowed;
            opacity: 0.6;
        }

        h3 {
            color: #FFD700;
            margin-bottom: 10px;
            border-bottom: 2px solid rgba(255, 215, 0, 0.3);
            padding-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="sidebar">
            <div class="status-panel">
                <h3>🔗 连接状态</h3>
                <div class="status-item">
                    <span class="status-label">WebSocket:</span>
                    <span id="ws-status" class="status-value">未连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">数据更新:</span>
                    <span id="data-updates" class="status-value">0</span>
                </div>
                <div class="status-item">
                    <span class="status-label">最后更新:</span>
                    <span id="last-update" class="status-value">无</span>
                </div>
                <button id="connect-btn" onclick="connectWebSocket()">连接</button>
                <button id="disconnect-btn" onclick="disconnectWebSocket()" disabled>断开</button>
            </div>

            <div class="units-panel">
                <h3>⚔️ 战场单位</h3>
                <div id="units-list">
                    <p style="color: #B0C4DE; text-align: center;">等待数据...</p>
                </div>
            </div>

            <div class="logs-panel">
                <h3>📝 实时日志</h3>
                <div id="logs-container">
                    <div class="log-entry">
                        <span class="log-timestamp">[系统]</span>
                        <span class="log-message">三维显示系统已加载</span>
                    </div>
                </div>
                <button onclick="clearLogs()">清空日志</button>
            </div>
        </div>

        <div class="main-display">
            <div class="connection-status">
                <span id="connection-indicator" class="disconnected">🔴 未连接</span>
            </div>
            
            <div class="map-container" id="map-container">
                <!-- 战场单位将在这里显示 -->
            </div>
            
            <div class="unit-tooltip" id="unit-tooltip"></div>
        </div>
    </div>

    <script>
        // 三维显示WebSocket客户端
        class ThreeDWebSocketClient {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.wsUrl = 'ws://localhost:8000/ws/threed';
                this.dataUpdates = 0;
                this.units = { enemy_units: [], friendly_units: [] };
                
                this.init();
            }
            
            init() {
                console.log('🚀 初始化三维显示WebSocket客户端...');
                this.addLog('系统', '正在启动三维显示客户端...');
                
                // 自动连接
                setTimeout(() => {
                    this.connectWebSocket();
                }, 1000);
            }
            
            connectWebSocket() {
                if (this.isConnected) {
                    this.addLog('连接', 'WebSocket已经连接');
                    return;
                }
                
                try {
                    this.addLog('连接', `正在连接WebSocket: ${this.wsUrl}`);
                    this.ws = new WebSocket(this.wsUrl);
                    
                    this.ws.onopen = (event) => {
                        this.onWebSocketOpen(event);
                    };
                    
                    this.ws.onmessage = (event) => {
                        this.onWebSocketMessage(event);
                    };
                    
                    this.ws.onclose = (event) => {
                        this.onWebSocketClose(event);
                    };
                    
                    this.ws.onerror = (event) => {
                        this.onWebSocketError(event);
                    };
                    
                } catch (error) {
                    this.addLog('连接错误', `WebSocket连接失败: ${error.message}`);
                    this.updateConnectionStatus('🔴 连接失败');
                }
            }
            
            onWebSocketOpen(event) {
                console.log('✅ 三维显示WebSocket连接已建立');
                this.isConnected = true;
                
                this.updateConnectionStatus('🟢 已连接');
                this.updateElement('ws-status', '🟢 已连接');
                document.getElementById('connect-btn').disabled = true;
                document.getElementById('disconnect-btn').disabled = false;
                
                this.addLog('连接成功', '三维显示WebSocket连接已建立');
            }
            
            onWebSocketMessage(event) {
                try {
                    const message = JSON.parse(event.data);
                    
                    switch (message.type) {
                        case 'connection':
                            this.addLog('连接', message.message);
                            break;
                        case 'threed_units':
                            this.handleUnitsData(message);
                            break;
                        case 'pong':
                            this.addLog('心跳', '收到服务器心跳响应');
                            break;
                        default:
                            this.addLog('消息', `未知消息类型: ${message.type}`);
                    }
                    
                } catch (error) {
                    console.error('❌ 解析WebSocket消息失败:', error);
                    this.addLog('解析错误', `消息解析失败: ${error.message}`);
                }
            }
            
            handleUnitsData(message) {
                this.dataUpdates++;
                this.units = message.data;
                
                this.updateElement('data-updates', this.dataUpdates);
                this.updateElement('last-update', new Date().toLocaleTimeString());
                
                this.updateUnitsDisplay();
                this.updateMapDisplay();
                
                this.addLog('数据更新', `收到单位数据 - 敌方:${this.units.enemy_units.length}, 我方:${this.units.friendly_units.length}`);
            }
            
            updateUnitsDisplay() {
                const container = document.getElementById('units-list');
                let html = '';
                
                // 显示敌方单位
                this.units.enemy_units.forEach(unit => {
                    html += `
                        <div class="unit-item unit-enemy">
                            <div class="unit-name">${unit.name}</div>
                            <div class="unit-position">位置: ${unit.position.longitude.toFixed(4)}, ${unit.position.latitude.toFixed(4)}</div>
                            <div class="unit-status">血量: ${unit.status.health.toFixed(1)}% | 威胁: ${unit.threat_level}</div>
                        </div>
                    `;
                });
                
                // 显示我方单位
                this.units.friendly_units.forEach(unit => {
                    html += `
                        <div class="unit-item unit-friendly">
                            <div class="unit-name">${unit.name}</div>
                            <div class="unit-position">位置: ${unit.position.longitude.toFixed(4)}, ${unit.position.latitude.toFixed(4)}</div>
                            <div class="unit-status">血量: ${unit.status.health.toFixed(1)}% | 燃料: ${unit.status.fuel.toFixed(1)}%</div>
                        </div>
                    `;
                });
                
                container.innerHTML = html || '<p style="color: #B0C4DE; text-align: center;">暂无单位数据</p>';
            }
            
            updateMapDisplay() {
                const mapContainer = document.getElementById('map-container');
                
                // 清除现有标记
                const existingMarkers = mapContainer.querySelectorAll('.unit-marker');
                existingMarkers.forEach(marker => marker.remove());
                
                // 计算地图范围
                const mapWidth = mapContainer.clientWidth;
                const mapHeight = mapContainer.clientHeight;
                
                // 简单的坐标转换（假设经纬度范围）
                const lonMin = 116.0, lonMax = 117.0;
                const latMin = 39.0, latMax = 40.0;
                
                // 添加敌方单位标记
                this.units.enemy_units.forEach(unit => {
                    const x = ((unit.position.longitude - lonMin) / (lonMax - lonMin)) * mapWidth;
                    const y = ((latMax - unit.position.latitude) / (latMax - latMin)) * mapHeight;
                    
                    const marker = this.createUnitMarker(unit, x, y, 'enemy');
                    mapContainer.appendChild(marker);
                });
                
                // 添加我方单位标记
                this.units.friendly_units.forEach(unit => {
                    const x = ((unit.position.longitude - lonMin) / (lonMax - lonMin)) * mapWidth;
                    const y = ((latMax - unit.position.latitude) / (latMax - latMin)) * mapHeight;
                    
                    const marker = this.createUnitMarker(unit, x, y, 'friendly');
                    mapContainer.appendChild(marker);
                });
            }
            
            createUnitMarker(unit, x, y, type) {
                const marker = document.createElement('div');
                marker.className = `unit-marker ${type}`;
                marker.style.left = `${x - 10}px`;
                marker.style.top = `${y - 10}px`;
                marker.textContent = type === 'enemy' ? 'E' : 'F';
                
                // 添加悬停事件
                marker.addEventListener('mouseenter', (e) => {
                    this.showTooltip(e, unit);
                });
                
                marker.addEventListener('mouseleave', () => {
                    this.hideTooltip();
                });
                
                return marker;
            }
            
            showTooltip(event, unit) {
                const tooltip = document.getElementById('unit-tooltip');
                tooltip.innerHTML = `
                    <strong>${unit.name}</strong><br>
                    类型: ${unit.type}<br>
                    位置: ${unit.position.longitude.toFixed(4)}, ${unit.position.latitude.toFixed(4)}<br>
                    血量: ${unit.status.health.toFixed(1)}%<br>
                    速度: ${unit.speed} km/h
                `;
                tooltip.style.left = `${event.pageX + 10}px`;
                tooltip.style.top = `${event.pageY - 10}px`;
                tooltip.style.display = 'block';
            }
            
            hideTooltip() {
                const tooltip = document.getElementById('unit-tooltip');
                tooltip.style.display = 'none';
            }
            
            onWebSocketClose(event) {
                console.log('❌ 三维显示WebSocket连接已关闭:', event);
                this.isConnected = false;
                
                this.updateConnectionStatus('🔴 已断开');
                this.updateElement('ws-status', '🔴 已断开');
                document.getElementById('connect-btn').disabled = false;
                document.getElementById('disconnect-btn').disabled = true;
                
                this.addLog('连接断开', `WebSocket连接已关闭 (代码: ${event.code})`);
            }
            
            onWebSocketError(event) {
                console.error('❌ 三维显示WebSocket错误:', event);
                this.addLog('连接错误', 'WebSocket连接发生错误');
            }
            
            disconnectWebSocket() {
                if (this.ws) {
                    this.ws.close();
                    this.addLog('手动断开', '用户主动断开WebSocket连接');
                }
            }
            
            updateConnectionStatus(status) {
                this.updateElement('connection-indicator', status);
            }
            
            updateElement(id, content) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = content;
                }
            }
            
            addLog(type, message) {
                const container = document.getElementById('logs-container');
                const timestamp = new Date().toLocaleTimeString();
                
                const logEntry = document.createElement('div');
                logEntry.className = 'log-entry';
                logEntry.innerHTML = `
                    <span class="log-timestamp">[${timestamp}]</span>
                    <span class="log-message">[${type}] ${message}</span>
                `;
                
                container.appendChild(logEntry);
                container.scrollTop = container.scrollHeight;
                
                // 限制日志条数
                const logs = container.children;
                if (logs.length > 50) {
                    container.removeChild(logs[0]);
                }
            }
        }

        // 全局变量
        let threeDClient;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 页面加载完成，初始化三维显示WebSocket客户端...');
            threeDClient = new ThreeDWebSocketClient();
        });

        // 全局函数
        function connectWebSocket() {
            if (threeDClient) {
                threeDClient.connectWebSocket();
            }
        }

        function disconnectWebSocket() {
            if (threeDClient) {
                threeDClient.disconnectWebSocket();
            }
        }

        function clearLogs() {
            const container = document.getElementById('logs-container');
            container.innerHTML = '';
            threeDClient.addLog('系统', '日志已清空');
        }
    </script>
</body>
</html>
