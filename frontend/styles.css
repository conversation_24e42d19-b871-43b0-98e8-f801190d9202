/* WebSocket实时战场态势分析系统样式 */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: #ffffff;
    min-height: 100vh;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 15px;
    backdrop-filter: blur(10px);
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.status-bar {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    flex-wrap: wrap;
}

.status-indicator {
    font-size: 1.2em;
    font-weight: bold;
    padding: 8px 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 20px;
}

button {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

button:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

button:disabled {
    background: #666;
    cursor: not-allowed;
    opacity: 0.6;
}

.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.panel {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.panel:hover {
    transform: translateY(-5px);
}

.panel h2 {
    font-size: 1.5em;
    margin-bottom: 15px;
    color: #FFD700;
    border-bottom: 2px solid rgba(255, 215, 0, 0.3);
    padding-bottom: 10px;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.status-item {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.status-item .label {
    font-weight: bold;
    color: #B0C4DE;
}

.status-item .value {
    color: #FFD700;
    font-weight: bold;
}

.no-data {
    text-align: center;
    color: #B0C4DE;
    font-style: italic;
    padding: 20px;
}

/* 威胁评估样式 */
.threat-data {
    space-y: 15px;
}

.threat-level {
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    font-size: 1.1em;
    font-weight: bold;
}

.threat-level.critical {
    background: rgba(220, 20, 60, 0.3);
    border-left: 4px solid #DC143C;
}

.threat-level.high {
    background: rgba(255, 69, 0, 0.3);
    border-left: 4px solid #FF4500;
}

.threat-level.medium {
    background: rgba(255, 165, 0, 0.3);
    border-left: 4px solid #FFA500;
}

.threat-level.low {
    background: rgba(50, 205, 50, 0.3);
    border-left: 4px solid #32CD32;
}

.main-threat, .strategy, .priority-target, .deployment {
    background: rgba(0, 0, 0, 0.2);
    padding: 12px;
    border-radius: 8px;
    margin-bottom: 10px;
}

.threat-ranking ol, .precautions ul {
    margin-left: 20px;
    margin-top: 10px;
}

.threat-ranking li, .precautions li {
    margin-bottom: 5px;
    padding: 5px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
}

/* 日志和消息样式 */
#log-container, #message-container {
    max-height: 300px;
    overflow-y: auto;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 10px;
}

.log-entry, .message-entry {
    margin-bottom: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 5px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.log-entry .timestamp, .message-entry .timestamp {
    color: #87CEEB;
    font-weight: bold;
}

.log-entry .type {
    color: #FFD700;
    font-weight: bold;
    margin: 0 10px;
}

.log-entry .message {
    color: #F0F8FF;
}

.message-entry .content {
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 5px;
    margin-top: 5px;
    white-space: pre-wrap;
    font-size: 0.8em;
    max-height: 200px;
    overflow-y: auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .status-bar {
        flex-direction: column;
        gap: 10px;
    }
    
    .dashboard {
        grid-template-columns: 1fr;
    }
    
    .status-grid {
        grid-template-columns: 1fr;
    }
}

/* 动画效果 */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.status-indicator {
    animation: pulse 2s infinite;
}

/* 特殊状态样式 */
.connected {
    color: #32CD32 !important;
    animation: none;
}

.disconnected {
    color: #DC143C !important;
}

.connecting {
    color: #FFA500 !important;
}
