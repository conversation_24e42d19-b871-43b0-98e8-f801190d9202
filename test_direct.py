#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试MQTT接收器
"""
import sys
import os

print("开始测试...")

try:
    print("导入MQTT接收器...")
    from mqtt_data_receiver import MQTTDataReceiver
    print("导入成功")
    
    print("创建实例...")
    receiver = MQTTDataReceiver()
    print("创建成功")
    
    print(f"我方单位数量: {len(receiver.friendly_units)}")
    
    if len(receiver.friendly_units) > 0:
        print("前3个单位:")
        for i, unit in enumerate(receiver.friendly_units[:3]):
            print(f"  {i+1}. ID={unit.id}, 名称={unit.name}")
    else:
        print("错误: 没有我方单位!")
        
except Exception as e:
    print(f"错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
