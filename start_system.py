#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统启动脚本
一键启动MQTT接收器和FastAPI服务
"""
import subprocess
import time
import sys
import os
import signal
import logging
from typing import List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SystemLauncher:
    """系统启动器"""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = False
        
    def start_fastapi(self):
        """启动FastAPI服务"""
        try:
            logger.info("🚀 启动FastAPI服务...")
            process = subprocess.Popen(
                [sys.executable, "main_fastapi.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes.append(process)
            logger.info("✅ FastAPI服务已启动 (PID: {})".format(process.pid))
            return process
        except Exception as e:
            logger.error(f"❌ 启动FastAPI服务失败: {e}")
            return None
    
    def start_mqtt_receiver(self):
        """启动MQTT接收器"""
        try:
            logger.info("📡 启动MQTT接收器...")
            process = subprocess.Popen(
                [sys.executable, "mqtt_data_receiver.py"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            self.processes.append(process)
            logger.info("✅ MQTT接收器已启动 (PID: {})".format(process.pid))
            return process
        except Exception as e:
            logger.error(f"❌ 启动MQTT接收器失败: {e}")
            return None
    
    def check_dependencies(self):
        """检查依赖文件"""
        required_files = [
            "main_fastapi.py",
            "mqtt_data_receiver.py",
            "data_flow_controller.py",
            "config/mqtt_config.json"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not os.path.exists(file_path):
                missing_files.append(file_path)
        
        if missing_files:
            logger.error("❌ 缺少必要文件:")
            for file_path in missing_files:
                logger.error(f"   - {file_path}")
            return False
        
        logger.info("✅ 依赖文件检查通过")
        return True
    
    def wait_for_fastapi_ready(self, timeout=30):
        """等待FastAPI服务就绪"""
        import requests
        
        logger.info("⏳ 等待FastAPI服务就绪...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                response = requests.get("http://localhost:8000/health", timeout=2)
                if response.status_code == 200:
                    logger.info("✅ FastAPI服务已就绪")
                    return True
            except:
                pass
            time.sleep(1)
        
        logger.warning("⚠️  FastAPI服务启动超时，但继续启动MQTT接收器")
        return False
    
    def monitor_processes(self):
        """监控进程状态"""
        logger.info("👀 开始监控系统进程...")
        logger.info("按 Ctrl+C 停止系统")
        
        try:
            while self.running:
                # 检查进程状态
                for i, process in enumerate(self.processes):
                    if process.poll() is not None:
                        logger.error(f"❌ 进程 {i+1} 已退出 (返回码: {process.returncode})")
                        # 读取错误输出
                        stderr = process.stderr.read()
                        if stderr:
                            logger.error(f"错误输出: {stderr}")
                
                time.sleep(5)  # 每5秒检查一次
                
        except KeyboardInterrupt:
            logger.info("收到停止信号")
        finally:
            self.stop_all_processes()
    
    def stop_all_processes(self):
        """停止所有进程"""
        logger.info("🛑 正在停止所有进程...")
        
        for i, process in enumerate(self.processes):
            if process.poll() is None:  # 进程仍在运行
                logger.info(f"停止进程 {i+1} (PID: {process.pid})")
                try:
                    process.terminate()
                    # 等待进程优雅退出
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    logger.warning(f"强制杀死进程 {i+1}")
                    process.kill()
                except Exception as e:
                    logger.error(f"停止进程 {i+1} 时出错: {e}")
        
        self.processes.clear()
        logger.info("✅ 所有进程已停止")
    
    def start_system(self):
        """启动整个系统"""
        logger.info("🎯 开始启动战场数据处理系统")
        
        # 检查依赖
        if not self.check_dependencies():
            return False
        
        self.running = True
        
        try:
            # 1. 启动FastAPI服务
            fastapi_process = self.start_fastapi()
            if not fastapi_process:
                return False
            
            # 2. 等待FastAPI就绪
            self.wait_for_fastapi_ready()
            
            # 3. 启动MQTT接收器
            mqtt_process = self.start_mqtt_receiver()
            if not mqtt_process:
                return False
            
            # 4. 等待一下让服务稳定
            time.sleep(2)
            
            logger.info("🎉 系统启动完成!")
            logger.info("📊 服务状态:")
            logger.info("   - FastAPI服务: http://localhost:8000")
            logger.info("   - API文档: http://localhost:8000/docs")
            logger.info("   - MQTT接收器: 运行中")
            logger.info("   - 数据流控制器: 运行中")
            
            # 5. 监控进程
            self.monitor_processes()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            self.stop_all_processes()
            return False

def main():
    """主程序"""
    launcher = SystemLauncher()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        logger.info("收到停止信号")
        launcher.running = False
        launcher.stop_all_processes()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        success = launcher.start_system()
        if not success:
            logger.error("系统启动失败")
            sys.exit(1)
    except Exception as e:
        logger.error(f"启动过程中发生错误: {e}")
        launcher.stop_all_processes()
        sys.exit(1)

if __name__ == "__main__":
    main()
