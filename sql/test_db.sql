/*
 Navicat Premium Dump SQL

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80037 (8.0.37)
 Source Host           : *************:3306
 Source Schema         : test_db

 Target Server Type    : MySQL
 Target Server Version : 80037 (8.0.37)
 File Encoding         : 65001

 Date: 22/09/2025 13:15:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for friendly_forces
-- ----------------------------
DROP TABLE IF EXISTS `friendly_forces`;
CREATE TABLE `friendly_forces`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `unit_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部队编号',
  `unit_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部队名称',
  `unit_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部队类型',
  `weapon_systems` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '武器系统(JSON数组)',
  `personnel_count` int NULL DEFAULT NULL COMMENT '人员数量',
  `vehicle_count` int NULL DEFAULT NULL COMMENT '车辆数量',
  `current_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '当前位置',
  `operational_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'ready' COMMENT '作战状态',
  `supply_level` decimal(3, 2) NULL DEFAULT 1.00 COMMENT '补给水平(0-1)',
  `morale_level` decimal(3, 2) NULL DEFAULT 0.80 COMMENT '士气水平(0-1)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_unit_type`(`unit_type` ASC) USING BTREE,
  INDEX `idx_status`(`operational_status` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '我方部队信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of friendly_forces
-- ----------------------------
INSERT INTO `friendly_forces` VALUES (1, 'TANK-001', '第1装甲营', '装甲部队', '[\"99A主战坦克\", \"04A步兵战车\", \"PLZ-05自行榴弹炮\"]', 300, 45, '基地待命', 'ready', 0.95, 0.85);
INSERT INTO `friendly_forces` VALUES (2, 'INF-001', '第1机械化步兵营', '机械化步兵', '[\"95式突击步枪\", \"红箭-12反坦克导弹\", \"89式装甲车\"]', 400, 30, '前沿阵地', 'ready', 0.90, 0.80);
INSERT INTO `friendly_forces` VALUES (3, 'AIR-001', '第1战斗机中队', '空军', '[\"歼-20战斗机\", \"歼-16战斗机\"]', 50, 24, '空军基地', 'ready', 0.98, 0.90);

-- ----------------------------
-- Table structure for items
-- ----------------------------
DROP TABLE IF EXISTS `items`;
CREATE TABLE `items`  (
  `id` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT (uuid()) COMMENT '主键 UUID',
  `name_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '武器ID',
  `category_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '武器名称',
  `category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '武器信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of items
-- ----------------------------
INSERT INTO `items` VALUES ('c4615c23-938a-11f0-9fb1-fefcfe5e5311', '0101', '01', '歼-20 战斗机', '飞机', '第五代隐形多用途战斗机，机组：1人，最大速度：约Mach2+，作战半径：约1,200–1,500 km，作战高度：高空超音速，武器：内部/外挂空空导弹与机炮，用途：制空/突防（信息为近似公开资料）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616031-938a-11f0-9fb1-fefcfe5e5311', '0102', '01', 'F-35 闪电 II', '飞机', '隐形多用途战斗机，机组：1人，最大速度：约Mach1.6，作战半径：约1,000–1,300 km，具备低可探测特性，武器：对空/对地精确制导武器，内置传感器融合能力（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46161a6-938a-11f0-9fb1-fefcfe5e5311', '0103', '01', '苏-57', '飞机', '第五代隐形/超机动战斗机，机组：1人，最大速度：约Mach2+，作战半径：约1,500 km，强调超机动与多谱态传感器，武器：空空导弹/对地武器（为近似公开信息）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616236-938a-11f0-9fb1-fefcfe5e5311', '0201', '02', '99A 主战坦克', '坦克', '第三代主战坦克，乘员：3人，作战重量：约55吨，主炮：125mm 光滑炮，最高公路速度：约65 km/h，防护与火控系统集成，适用于机动作战（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46162db-938a-11f0-9fb1-fefcfe5e5311', '0202', '02', 'M1A2 艾布拉姆斯', '坦克', '美军主战坦克，乘员：4人，作战重量：约60–70吨，主炮：120mm ，动力：燃气轮机，最高公路速度：约67 km/h，配备复合装甲与主动/被动防护系统（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616374-938a-11f0-9fb1-fefcfe5e5311', '0203', '02', 'T-90MS', '坦克', '俄系主战坦克，乘员：3人，作战重量：约46–48吨，主炮：125mm ，具备反应装甲与遥控武器站，最高速度：约60–65 km/h（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c461644b-938a-11f0-9fb1-fefcfe5e5311', '0301', '03', '东风-41', '导弹', '洲际弹道导弹（ICBM）类别，机动/固体燃料或列装版本差异，射程：数千至上万公里级，投送能力：可携带多弹头/分导（公开分类参数，非操作性细节）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46164ac-938a-11f0-9fb1-fefcfe5e5311', '0302', '03', 'S-400 防空导弹', '导弹', '远程防空/反导系统，拦截射程：百公里级别（随拦截弹型不同），系统包括多种型号拦截弹与雷达探测单元，适用于高/中/低空目标防护（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616514-938a-11f0-9fb1-fefcfe5e5311', '0303', '03', '爱国者 PAC-3', '导弹', '中远程防空/战区防空系统，拦截能力针对来袭导弹与飞机，采用雷达导引与拦截弹，射程与性能随改型不同（为近似公开资料描述）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616567-938a-11f0-9fb1-fefcfe5e5311', '0401', '04', '辽宁号 航母', '舰艇', '滑跃/短距起飞航母，排水量：约6–7万吨级，搭载固定翼与直升机若干，功能：舰载航空作战/海上力量投送（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46165cc-938a-11f0-9fb1-fefcfe5e5311', '0402', '04', '尼米兹级 航母', '舰艇', '核动力航母，排水量：约10万吨级以上，机翼作战群搭载量大，续航与行动能力强，承担远洋航母战斗群核心任务（近似公开信息）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616658-938a-11f0-9fb1-fefcfe5e5311', '0403', '04', '宙斯盾驱逐舰', '舰艇', '综合防空/反导驱逐舰平台，装备相控阵雷达与垂直发射系统，可执行防空、反舰、反潜等任务，排水量与装备视各国型号不同（近似公开描述）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46166e9-938a-11f0-9fb1-fefcfe5e5311', '0501', '05', '武直-10', '直升机', '国产武装直升机，乘员：2人，最高速度：约300 km/h，武装：导弹/火箭/机炮组合，任务：近支援与反装甲作战（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c4616781-938a-11f0-9fb1-fefcfe5e5311', '0502', '05', 'AH-64 阿帕奇', '直升机', '攻击直升机，乘员：2人，最高速度：约293 km/h，武装：反坦克导弹、火箭与机炮，具备火控雷达与夜视能力（近似公开参数）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');
INSERT INTO `items` VALUES ('c46167ec-938a-11f0-9fb1-fefcfe5e5311', '0503', '05', '米-28', '直升机', '俄罗斯攻击直升机，乘员：2人，最高速度：约300 km/h，武装：反装甲导弹、火箭与机炮，注重生存性与火力（近似公开资料）', '2025-09-17 13:54:25', '2025-09-17 13:54:25');

-- ----------------------------
-- Table structure for weapon_counter_matrix
-- ----------------------------
DROP TABLE IF EXISTS `weapon_counter_matrix`;
CREATE TABLE `weapon_counter_matrix`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `attacker_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '攻击方武器类型',
  `target_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '目标武器类型',
  `effectiveness_score` decimal(3, 2) NOT NULL COMMENT '有效性评分(0-1)',
  `engagement_range` decimal(8, 2) NULL DEFAULT NULL COMMENT '交战距离(km)',
  `success_probability` decimal(3, 2) NULL DEFAULT NULL COMMENT '成功概率',
  `recommended_tactics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '推荐战术',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_counter`(`attacker_type` ASC, `target_type` ASC) USING BTREE,
  INDEX `idx_attacker`(`attacker_type` ASC) USING BTREE,
  INDEX `idx_target`(`target_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '武器克制关系矩阵' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of weapon_counter_matrix
-- ----------------------------
INSERT INTO `weapon_counter_matrix` VALUES (1, '主战坦克', '装甲车', 0.95, 3.00, 0.90, '直射火力，装甲优势');
INSERT INTO `weapon_counter_matrix` VALUES (2, '主战坦克', '步兵', 0.85, 2.00, 0.85, '机枪扫射，碾压攻击');
INSERT INTO `weapon_counter_matrix` VALUES (3, '装甲车', '步兵', 0.80, 1.50, 0.75, '机枪火力，快速机动');
INSERT INTO `weapon_counter_matrix` VALUES (4, '战斗机', '主战坦克', 0.90, 10.00, 0.80, '空地导弹，俯冲攻击');
INSERT INTO `weapon_counter_matrix` VALUES (5, '战斗机', '装甲车', 0.95, 8.00, 0.85, '机炮扫射，精确制导');

-- ----------------------------
-- Table structure for weapon_knowledge
-- ----------------------------
DROP TABLE IF EXISTS `weapon_knowledge`;
CREATE TABLE `weapon_knowledge`  (
  `id` int NOT NULL AUTO_INCREMENT,
  `weapon_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '武器名称',
  `weapon_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '武器类型',
  `country` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生产国家',
  `manufacturer` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '制造商',
  `max_range` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大射程(km)',
  `max_speed` decimal(8, 2) NULL DEFAULT NULL COMMENT '最大速度(km/h)',
  `armor_thickness` decimal(6, 2) NULL DEFAULT NULL COMMENT '装甲厚度(mm)',
  `main_weapon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '主要武器',
  `crew_count` int NULL DEFAULT NULL COMMENT '乘员数量',
  `weight` decimal(8, 2) NULL DEFAULT NULL COMMENT '重量(吨)',
  `firepower_rating` int NULL DEFAULT NULL COMMENT '火力评级',
  `protection_rating` int NULL DEFAULT NULL COMMENT '防护评级',
  `mobility_rating` int NULL DEFAULT NULL COMMENT '机动性评级',
  `detection_rating` int NULL DEFAULT NULL COMMENT '探测能力评级',
  `terrain_adaptability` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '地形适应性(JSON格式)',
  `weather_limitations` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '天气限制(JSON格式)',
  `advantages` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主要优势',
  `weaknesses` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '主要弱点',
  `effective_against` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '有效对抗的武器类型(JSON数组)',
  `vulnerable_to` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '易受攻击的武器类型(JSON数组)',
  `recommended_tactics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '推荐战术',
  `counter_tactics` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '反制战术',
  `technical_specs` json NULL COMMENT '详细技术规格',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_weapon_name`(`weapon_name` ASC) USING BTREE,
  INDEX `idx_weapon_type`(`weapon_type` ASC) USING BTREE,
  INDEX `idx_name_type`(`weapon_name` ASC, `weapon_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '武器知识库表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of weapon_knowledge
-- ----------------------------
INSERT INTO `weapon_knowledge` VALUES (1, 'T-90主战坦克', '主战坦克', '俄罗斯', '乌拉尔车辆厂', 4.00, 60.00, 850.00, '125mm滑膛炮', 3, 46.50, 9, 9, 7, 6, '{\"urban\": 8, \"desert\": 9, \"forest\": 6, \"mountain\": 5}', '{\"rain\": 7, \"snow\": 6, \"fog\": 5}', '强大的火力，优秀的防护，先进的火控系统', '机动性相对较低，燃料消耗大，维护复杂', '[\"装甲车\", \"步兵战车\", \"反坦克炮\"]', '[\"攻击直升机\", \"反坦克导弹\", \"空中打击\"]', '集群作战，火力压制，装甲突击', '空中打击，侧翼包围，反坦克导弹伏击', '{\"armor\": \"复合装甲+反应装甲\", \"engine\": \"V-92S2柴油机\", \"transmission\": \"自动变速箱\"}', '2025-09-17 14:36:24', '2025-09-17 14:36:24');
INSERT INTO `weapon_knowledge` VALUES (2, 'BTR-80装甲运兵车', '装甲车', '俄罗斯', '高尔基汽车厂', 2.00, 80.00, 300.00, '14.5mm机枪', 10, 13.60, 5, 6, 9, 4, '{\"urban\": 9, \"desert\": 8, \"forest\": 7, \"mountain\": 6}', '{\"rain\": 8, \"snow\": 7, \"fog\": 6}', '高机动性，两栖能力，载员能力强', '装甲较薄，火力有限，易受重武器攻击', '[\"步兵\", \"轻型车辆\", \"工事\"]', '[\"主战坦克\", \"反坦克武器\", \"重机枪\"]', '快速机动，步兵输送，火力支援', '重武器打击，地雷阻击，狙击手攻击', '{\"engine\": \"KAMAZ-7403柴油机\", \"capacity\": \"7名步兵\", \"amphibious\": true}', '2025-09-17 14:36:24', '2025-09-17 14:36:24');
INSERT INTO `weapon_knowledge` VALUES (3, '歼-20战斗机', '战斗机', '中国', '成都飞机工业集团', 2000.00, 2100.00, 0.00, '航炮+导弹', 1, 19.00, 10, 8, 10, 10, '{\"all_terrain\": 10}', '{\"storm\": 3, \"fog\": 4}', '隐身能力强，超音速巡航，先进航电', '维护成本高，对基础设施要求高', '[\"战斗机\", \"轰炸机\", \"地面目标\"]', '[\"防空导弹\", \"电子干扰\", \"隐身战机\"]', '超视距攻击，隐身突防，制空作战', '防空网拦截，电子对抗，多层防御', '{\"engine\": \"WS-15涡扇发动机\", \"stealth\": true, \"supercruise\": true}', '2025-09-17 14:36:24', '2025-09-17 14:36:24');

SET FOREIGN_KEY_CHECKS = 1;
