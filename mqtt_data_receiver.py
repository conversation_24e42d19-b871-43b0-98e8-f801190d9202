#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MQTT数据接收器
接收真实数据源并转换为BattlefieldUnit格式
集成数据流控制器解决高频数据处理问题
"""
import json
import asyncio
import logging
import paho.mqtt.client as mqtt
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import requests
import time

# 导入数据流控制器
from data_flow_controller import DataFlowController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """位置信息"""
    longitude: float  # 经度
    latitude: float   # 纬度
    altitude: float   # 高度

@dataclass
class WeaponLoadout:
    """武器挂载信息"""
    weapon_type: str   # 弹药类型
    quantity: int      # 数量
    max_quantity: int  # 最大容量

@dataclass
class UnitStatus:
    """单位状态"""
    health: float           # 生命值 (0-100)
    fuel: float            # 燃料状态 (0-100)
    operational: bool      # 是否可操作
    weapons: List[WeaponLoadout]  # 武器挂载列表

@dataclass
class BattlefieldUnit:
    """战场单位"""
    id: str
    name: str          # 武器名称
    type: str          # 武器类型
    side: str          # 属方 (敌方/我方)
    position: Position
    status: UnitStatus
    threat_level: str  # 威胁等级
    confidence: float  # 置信度
    last_seen: str     # 最后发现时间
    speed: float       # 移动速度 (km/h)
    heading: float     # 航向角度 (0-360度)


class MQTTDataReceiver:
    """MQTT数据接收器 - 集成数据流控制器"""

    def __init__(self, config_file: str = "config/mqtt_config.json", api_url: str = "http://localhost:8010"):
        self.config_file = config_file
        self.api_url = api_url
        self.mqtt_client = None
        self.config = self._load_config()
        self.running = False

        # 初始化数据流控制器
        flow_controller_config = {
            "buffer_size": 100,
            "ai_analysis_interval": 30,  # 30秒分析一次
            "frontend_update_interval": 1,  # 1秒更新前端
            "change_threshold": 0.3,
            "max_concurrent_ai_tasks": 2,
            "api_url": api_url
        }
        self.flow_controller = DataFlowController(flow_controller_config)

        # 数据统计
        self.stats = {
            "total_messages": 0,
            "processed_units": 0,
            "failed_messages": 0,
            "start_time": time.time()
        }

        # 事件循环引用（用于在同步回调中调度异步任务）
        self.loop = None

        # 初始化我方单位数据
        logger.info("🚁 开始创建我方单位...")
        self.friendly_units = self._create_friendly_units()

        logger.info("MQTT数据接收器初始化完成 - 已集成数据流控制器")
        logger.info(f"✅ 已加载 {len(self.friendly_units)} 个我方单位")

        # 验证我方单位数据
        if len(self.friendly_units) == 0:
            logger.error("❌ 警告: 没有创建任何我方单位!")
        else:
            logger.info(f"📋 我方单位列表: {[unit.id for unit in self.friendly_units[:5]]}{'...' if len(self.friendly_units) > 5 else ''}")

    def _create_friendly_units(self) -> List[BattlefieldUnit]:
        """创建16个直-10我方单位 (ID: 0-15)，每架配置不同"""
        try:
            friendly_units = []
            logger.info("🚁 开始创建16个直-10我方单位...")

            # 手动创建16个不同配置的直-10单位
            units_data = [
                # 0号机
                BattlefieldUnit(
                    id="0",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.0, 40.0, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 1号机
                BattlefieldUnit(
                    id="1",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.001, 40.001, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 2号机
                BattlefieldUnit(
                    id="2",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.002, 40.002, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 3号机
                BattlefieldUnit(
                    id="3",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.003, 40.003, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 4号机
                BattlefieldUnit(
                    id="4",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                position=Position(116.004, 40.004, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
                # 5号机
                BattlefieldUnit(
                    id="5",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.005, 40.005, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 6号机
                BattlefieldUnit(
                    id="6",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.006, 40.006, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 7号机
                BattlefieldUnit(
                    id="7",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.007, 40.007, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 8号机
                BattlefieldUnit(
                    id="8",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.008, 40.008, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 9号机
                BattlefieldUnit(
                    id="9",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.009, 40.009, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 10号机
                BattlefieldUnit(
                    id="10",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.010, 40.010, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 11号机
                BattlefieldUnit(
                    id="11",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.011, 40.011, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 12号机
                BattlefieldUnit(
                    id="12",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.012, 40.012, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 13号机
                BattlefieldUnit(
                    id="13",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.013, 40.013, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 14号机
                BattlefieldUnit(
                    id="14",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.014, 40.014, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                ),
                # 15号机
                BattlefieldUnit(
                    id="15",
                    name="直-10",
                    type="武装直升机",
                    side="我方",
                    position=Position(116.015, 40.015, 1000.0),  # 您来补充具体位置
                    status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                    threat_level="我方",
                    confidence=1.0,
                    last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    speed=0.0,  # 您来补充
                    heading=0.0
                )
            ]

            friendly_units.extend(units_data)

            for unit in friendly_units:
                logger.info(f"创建我方单位: ID={unit.id}, {unit.name} - 位置: ({unit.position.longitude:.6f}, {unit.position.latitude:.6f}, {unit.position.altitude}m)")

            logger.info(f"✅ 成功创建 {len(friendly_units)} 个我方单位")
            return friendly_units

        except Exception as e:
            logger.error(f"❌ 创建我方单位失败: {e}")
            import traceback
            traceback.print_exc()
            return []


    def _load_config(self) -> Dict[str, Any]:
        """加载MQTT配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            logger.info(f"配置加载成功: {self.config_file}")
            return config["mqtt"]
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            # 返回默认配置
            return {
                "server_address": "tcp://************:1883",
                "username": "zkhd", 
                "password": "zk45619",
                "topic": "sys/esdk/status/8UUXN2Q00A01GE",
                "client_id": "battlefield_mqtt_client",
                "keepalive": 60,
                "qos": 1
            }
    
    def _parse_mqtt_data(self, mqtt_payload: str) -> List[BattlefieldUnit]:
        """解析MQTT数据并转换为BattlefieldUnit列表"""
        try:
            data = json.loads(mqtt_payload)
            logger.info(f"📨 收到MQTT原始数据:")
            logger.info(f"   Code: {data.get('code')}, ID: {data.get('id')}, Seq: {data.get('seq')}")
            logger.info(f"   时间: {data.get('time')}, 目标数: {len(data.get('objectList', []))}")

            # 可选：打印完整原始数据（调试用）
            if logger.level <= logging.DEBUG:
                logger.debug(f"   原始JSON: {mqtt_payload}")

            # 打印每个目标的原始数据
            for i, obj in enumerate(data.get('objectList', [])):
                logger.info(f"   目标{i+1}: class_name={obj.get('class_name')}, id={obj.get('id')}, "
                           f"confidence={obj.get('confidence')}, speed={obj.get('speed')}")
                logger.info(f"          位置: 经度{obj.get('loc_longitude')}, 纬度{obj.get('loc_latitude')}, 高度{obj.get('loc_height')}")
                if obj.get('url'):
                    logger.info(f"          图片: {obj.get('url')}")
            
            battlefield_units = []
            object_list = data.get('objectList', [])
            message_time = data.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            for obj in object_list:
                try:
                    # 转换为BattlefieldUnit
                    unit = BattlefieldUnit(
                        id=str(obj.get('id', 'unknown')),
                        name=obj.get('class_name', 'unknown'),
                        type=obj.get('class_name', 'unknown'),
                        side="敌方",
                        position=Position(
                            longitude=obj.get('loc_longitude', 0.0),
                            latitude=obj.get('loc_latitude', 0.0),
                            altitude=obj.get('loc_height', 0.0)
                        ),
                        status=UnitStatus(
                            health=100.0,
                            fuel=85.0,
                            operational=True,
                            weapons=[]  # 敌方武器信息暂时为空，可后续补充
                        ),
                        threat_level="中等",
                        confidence=obj.get('confidence', 0.5),
                        last_seen=message_time,
                        speed=obj.get('speed', 0.0),
                        heading=0.0
                    )
                    battlefield_units.append(unit)
                    
                    logger.info(f"转换单位: {unit.name} (ID: {unit.id}) - 位置: 经度{unit.position.longitude:.6f}, 纬度{unit.position.latitude:.6f}, 高度{unit.position.altitude:.2f}m - 速度{unit.speed}km/h, 置信度{unit.confidence:.2f}")
                    
                except Exception as e:
                    logger.error(f"转换单个目标失败: {e}")
                    continue
            
            self.stats["processed_units"] += len(battlefield_units)
            return battlefield_units
            
        except Exception as e:
            logger.error(f"解析MQTT数据失败: {e}")
            self.stats["failed_messages"] += 1
            return []
    
    def _create_battlefield_data(self, enemy_units: List[BattlefieldUnit]) -> Dict[str, Any]:
        """创建战场数据包"""
        logger.info(f"🏗️  创建战场数据包 - 敌方单位: {len(enemy_units)}个, 我方单位: {len(self.friendly_units)}个")
        # 强制检查我方单位
        if len(self.friendly_units) == 0:
            logger.error("❌ 严重错误: self.friendly_units 为空! 重新创建...")
            self.friendly_units = self._create_friendly_units()
            logger.info(f"🔄 重新创建后我方单位数量: {len(self.friendly_units)}")

        try:
            friendly_units_data = [asdict(unit) for unit in self.friendly_units]
            logger.info(f"✅ 我方单位序列化成功 - {len(friendly_units_data)}个")
        except Exception as e:
            logger.error(f"❌ 我方单位序列化失败: {e}")
            friendly_units_data = []


        battlefield_data = {
            "timestamp": datetime.now().isoformat(),
            "simulation_time": (time.time() - self.stats["start_time"]) / 60.0,  # 转换为分钟
            "enemy_units": [asdict(unit) for unit in enemy_units],
            "friendly_units": friendly_units_data,  # 使用序列化后的数据
            "battlefield_status": {
                "total_enemy": len(enemy_units),
                "total_friendly": len(self.friendly_units),
                "active_threats": len([u for u in enemy_units if u.threat_level in ["高", "极高"]])
            }
        }

        logger.info(f"📦 战场数据包创建完成 - 包含 {len(battlefield_data['friendly_units'])} 个我方单位")

        # 最终验证
        if len(battlefield_data['friendly_units']) == 0:
            logger.error("❌ 最终检查: 战场数据包中我方单位仍为空!")

        return battlefield_data
    
    async def _send_data_to_api(self, data: Dict[str, Any]):
        """发送数据到API进行分析"""
        try:
            logger.info(f"📤 准备发送数据到API - 敌方: {len(data.get('enemy_units', []))}个, 我方: {len(data.get('friendly_units', []))}个")

            response = requests.post(
                f"{self.api_url}/battlefield/analyze",
                json=data,
                timeout=30
            )
            if response.status_code == 200:
                logger.info(f"✅ 数据发送成功 - 响应时间: {response.elapsed.total_seconds():.2f}s")
                result = response.json()
                logger.info(f"分析结果状态: {result.get('status', 'unknown')}")
            else:
                logger.error(f"❌ API调用失败 - 状态码: {response.status_code}")
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ 发送数据失败: {e}")
        except Exception as e:
            logger.error(f"❌ 发送数据异常: {e}")
    
    async def _send_threed_data_to_api(self, data: Dict[str, Any]):
        """发送三维显示数据到API"""
        try:
            response = requests.post(
                f"{self.api_url}/threed/units",
                json=data,
                timeout=5
            )
            if response.status_code == 200:
                logger.debug(f"✅ 三维数据发送成功")
            else:
                logger.warning(f"❌ 三维数据发送失败 - 状态码: {response.status_code}")
        except Exception as e:
            logger.warning(f"❌ 发送三维数据时发生错误: {e}")
    
    def on_connect(self, client, userdata, flags, rc):
        """MQTT连接回调"""
        if rc == 0:
            logger.info(f"✅ MQTT连接成功")
            logger.info(f"连接标志: {flags}")

            # 订阅主题
            try:
                result = client.subscribe(self.config["topic"], self.config.get("qos", 1))
                logger.info(f"📡 已订阅主题: {self.config['topic']}, 结果: {result}")
            except Exception as e:
                logger.error(f"❌ 订阅主题失败: {e}")
        else:
            logger.error(f"❌ MQTT连接失败 - 返回码: {rc}")
            self._print_error_code_meaning(rc)

    def on_message(self, client, userdata, msg):
        """MQTT消息回调 - 使用数据流控制器处理高频数据"""
        try:
            self.stats["total_messages"] += 1
            payload = msg.payload.decode('utf-8')

            logger.debug(f"📨 收到MQTT消息 - 主题: {msg.topic}, 大小: {len(payload)} bytes")

            # 解析数据
            enemy_units = self._parse_mqtt_data(payload)

            # 使用已经创建好的我方单位数据
            friend_units = self.friendly_units

            if enemy_units:
                # 线程安全地调度异步任务
                if self.loop and self.loop.is_running():
                    # 使用call_soon_threadsafe在事件循环中调度任务
                    self.loop.call_soon_threadsafe(
                        self._schedule_async_task, enemy_units, friend_units
                    )
                    logger.debug(f"🎯 数据已提交到流控制器 - 敌方: {len(enemy_units)}个, 我方: {len(friend_units)}个")
                else:
                    # 如果没有事件循环，直接同步处理（降级方案）
                    logger.warning("⚠️  事件循环未运行，使用同步处理")
                    self._process_data_sync(enemy_units, friend_units)

                logger.debug(f"🎯 数据处理完成 - 敌方单位: {len(enemy_units)}个, 我方单位: {len(friend_units)}个")
            else:
                logger.warning("⚠️  未解析到有效单位数据")

        except Exception as e:
            logger.error(f"❌ 处理MQTT消息失败: {e}")
            self.stats["failed_messages"] += 1

    def _schedule_async_task(self, enemy_units, friend_units):
        """在事件循环中调度异步任务"""
        try:
            asyncio.create_task(self.flow_controller.process_mqtt_data(enemy_units, friend_units))
        except Exception as e:
            logger.error(f"❌ 调度异步任务失败: {e}")

    def _process_data_sync(self, enemy_units, friend_units):
        """同步处理数据（降级方案）"""
        try:
            # 创建战场数据包
            battlefield_data = {
                "timestamp": datetime.now().isoformat(),
                "simulation_time": (time.time() - self.stats["start_time"]) / 60.0,
                "enemy_units": [asdict(unit) for unit in enemy_units],
                "friendly_units": [asdict(unit) for unit in friend_units],
                "battlefield_status": {
                    "total_enemy": len(enemy_units),
                    "total_friendly": len(friend_units),
                    "active_threats": len([u for u in enemy_units if u.threat_level in ["高", "极高"]])
                }
            }
            logger.info(f"📦 同步处理 - 战场数据包包含: 敌方{len(battlefield_data.get('enemy_units', []))}个, 我方{len(battlefield_data.get('friendly_units', []))}个")

            # 同步发送到API（不使用异步）
            import requests
            try:
                response = requests.post(
                    f"{self.api_url}/battlefield/analyze",
                    json=battlefield_data,
                    timeout=5  # 较短的超时时间
                )
                if response.status_code == 200:
                    logger.debug("✅ 同步数据发送成功")
                else:
                    logger.warning(f"❌ 同步数据发送失败 - 状态码: {response.status_code}")
            except Exception as e:
                logger.warning(f"❌ 同步发送数据失败: {e}")

        except Exception as e:
            logger.error(f"❌ 同步处理数据失败: {e}")

    def on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        logger.warning(f"⚠️  MQTT连接断开 - 返回码: {rc}")
        self._print_error_code_meaning(rc)

        if rc != 0:
            logger.error("🔍 意外断开连接，可能原因:")
            logger.error("   1. 网络不稳定或防火墙阻断")
            logger.error("   2. MQTT broker重启或过载")
            logger.error("   3. 客户端ID冲突")
            logger.error("   4. Keep-alive超时")
            logger.error("   5. 认证信息过期")

    def on_subscribe(self, client, userdata, mid, granted_qos):
        """订阅成功回调"""
        logger.info(f"✅ 订阅成功 - 消息ID: {mid}, QoS: {granted_qos}")

    def on_log(self, client, userdata, level, buf):
        """MQTT日志回调"""
        logger.debug(f"MQTT日志: {buf}")

    def _print_error_code_meaning(self, rc):
        """打印错误码含义"""
        error_codes = {
            0: "连接成功",
            1: "协议版本不正确",
            2: "客户端ID无效",
            3: "服务器不可用",
            4: "用户名或密码错误",
            5: "未授权",
            6: "意外断开",
            7: "没有连接/连接丢失",
            8: "连接丢失",
            9: "连接超时",
            10: "协议错误"
        }

        meaning = error_codes.get(rc, f"未知错误码: {rc}")
        logger.info(f"错误码 {rc} 含义: {meaning}")
    
    async def start_mqtt_client(self):
        """启动MQTT客户端和数据流控制器"""
        try:
            # 获取当前事件循环
            self.loop = asyncio.get_running_loop()
            logger.info("✅ 事件循环已设置")

            # 启动数据流控制器的工作协程
            asyncio.create_task(self.flow_controller.ai_analysis_worker())
            asyncio.create_task(self.flow_controller.frontend_update_worker())
            logger.info("🔄 数据流控制器工作协程已启动")

            # 创建MQTT客户端（添加时间戳确保唯一性）
            import time
            unique_client_id = f"{self.config.get('client_id', 'battlefield_mqtt_client')}_{int(time.time())}"
            self.mqtt_client = mqtt.Client(unique_client_id)

            # 设置回调函数
            self.mqtt_client.on_connect = self.on_connect
            self.mqtt_client.on_message = self.on_message
            self.mqtt_client.on_disconnect = self.on_disconnect
            self.mqtt_client.on_subscribe = self.on_subscribe
            self.mqtt_client.on_log = self.on_log

            # 设置用户名和密码
            self.mqtt_client.username_pw_set(
                self.config["username"],
                self.config["password"]
            )

            # 设置连接选项
            self.mqtt_client.reconnect_delay_set(min_delay=1, max_delay=120)

            logger.info(f"🆔 使用客户端ID: {unique_client_id}")

            # 解析服务器地址
            server_address = self.config["server_address"]
            if server_address.startswith("tcp://"):
                host = server_address.replace("tcp://", "").split(":")[0]
                port = int(server_address.replace("tcp://", "").split(":")[1])
            else:
                host = server_address.split(":")[0]
                port = int(server_address.split(":")[1]) if ":" in server_address else 1883

            logger.info(f"🔗 正在连接MQTT服务器: {host}:{port}")

            # 连接到MQTT服务器
            self.mqtt_client.connect(host, port, self.config.get("keepalive", 60))

            # 启动循环
            self.running = True
            self.mqtt_client.loop_start()

            logger.info("🚀 MQTT客户端启动成功")

        except Exception as e:
            logger.error(f"❌ 启动MQTT客户端失败: {e}")
            raise
    
    def stop_mqtt_client(self):
        """停止MQTT客户端"""
        self.running = False
        if self.mqtt_client:
            self.mqtt_client.loop_stop()
            self.mqtt_client.disconnect()
            logger.info("🛑 MQTT客户端已停止")
    
    def print_stats(self):
        """打印统计信息"""
        runtime = time.time() - self.stats["start_time"]
        logger.info(f"📊 MQTT统计 - 运行时间: {runtime:.1f}s, 总消息: {self.stats['total_messages']}, "
                   f"处理单位: {self.stats['processed_units']}, 失败消息: {self.stats['failed_messages']}")

        # 打印数据流控制器统计
        self.flow_controller.print_stats()

async def main():
    """主程序"""
    logger.info("🚀 启动MQTT数据接收器 - 集成数据流控制器")

    # 创建MQTT接收器
    receiver = MQTTDataReceiver()

    try:
        # 启动MQTT客户端（现在是异步的）
        await receiver.start_mqtt_client()

        # 保持运行
        while receiver.running:
            await asyncio.sleep(10)
            receiver.print_stats()

    except KeyboardInterrupt:
        logger.info("收到停止信号")
    finally:
        receiver.stop_mqtt_client()
        receiver.print_stats()
        logger.info("程序已退出")

if __name__ == "__main__":
    asyncio.run(main())