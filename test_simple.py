#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试MQTT接收器初始化
"""
import sys
import os

print("🧪 开始简单测试...")

try:
    print("1. 测试基础导入...")
    import json
    import time
    import logging
    from datetime import datetime
    from dataclasses import dataclass, asdict
    from typing import List, Dict, Any
    print("✅ 基础模块导入成功")
    
    print("2. 测试MQTT模块...")
    import paho.mqtt.client as mqtt
    print("✅ MQTT模块导入成功")
    
    print("3. 测试requests模块...")
    import requests
    print("✅ requests模块导入成功")
    
    print("4. 测试asyncio模块...")
    import asyncio
    print("✅ asyncio模块导入成功")
    
    print("5. 测试MQTT接收器导入...")
    from mqtt_data_receiver import MQTTDataReceiver
    print("✅ MQTTDataReceiver导入成功")
    
    print("6. 测试MQTT接收器初始化...")
    receiver = MQTTDataReceiver()
    print("✅ MQTT接收器初始化成功")
    
    print(f"7. 检查我方单位数量: {len(receiver.friendly_units)}")
    
    if len(receiver.friendly_units) == 0:
        print("❌ 错误: 我方单位为空!")
        print("尝试手动调用创建方法...")
        units = receiver._create_friendly_units()
        print(f"手动创建结果: {len(units)} 个单位")
    else:
        print(f"✅ 我方单位创建成功: {len(receiver.friendly_units)} 个")
        for i, unit in enumerate(receiver.friendly_units[:3]):
            print(f"  单位{i+1}: ID={unit.id}, 名称={unit.name}")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
