
import asyncio
from dataclasses import dataclass
from datetime import datetime
import json
import logging
from typing import Any, Dict, List
import paho.mqtt.client as mqtt
import threading
import time

import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class Position:
    """位置信息"""
    longitude: float  # 经度
    latitude: float   # 纬度
    altitude: float   # 高度


@dataclass
class WeaponLoadout:
    """武器挂载信息"""
    weapon_type: str   # 弹药类型
    quantity: int      # 数量
    max_quantity: int  # 最大容量


@dataclass
class UnitStatus:
    """单位状态"""
    health: float           # 生命值 (0-100)
    fuel: float            # 燃料状态 (0-100)
    operational: bool      # 是否可操作
    weapons: List[WeaponLoadout]  # 武器挂载列表


@dataclass
class BattlefieldUnit:
    """战场单位"""
    id: str
    name: str          # 武器名称
    type: str          # 武器类型
    side: str          # 属方 (敌方/我方)
    position: Position
    status: UnitStatus
    threat_level: str  # 威胁等级
    confidence: float  # 置信度
    last_seen: str     # 最后发现时间
    speed: float       # 移动速度 (km/h)
    heading: float     # 航向角度 (0-360度)

class AsyncMQTTSubscriber:
    def __init__(self, broker, port=1883, username=None, password=None):
        self.client = mqtt.Client()
        self.broker = broker
        self.port = port
        
        # 设置用户名和密码（如果提供）
        if username and password:
            self.client.username_pw_set(username, password)
        
        self.client.on_connect = self.on_connect
        self.client.on_message = self.on_message
        self.client.on_disconnect = self.on_disconnect
        
        # 定义主题和对应的处理函数
        self.topic_handlers = {
            "sys/esdk/status/8UUXN2Q00A01GE": self.handle_theed,
            "thing/product/1581F8HGX255900A0DED/osd": self.handle_osd,
            # 可以继续添加更多主题和处理函数
        }

        self.api_url = "http://localhost:8010"

        self.received_data = {
            "sys/esdk/status/8UUXN2Q00A01GE": None,
            "thing/product/1581F8HGX255900A0DED/osd": None
        }

        # 创建并启动事件循环
        self.loop = asyncio.new_event_loop()
        self.loop_thread = None

        self.friendly_units = self._create_friendly_units()
        
    def on_connect(self, client, userdata, flags, rc):
        if rc == 0:
            print("连接成功!")
            # 订阅所有定义的主题
            for topic in self.topic_handlers.keys():
                client.subscribe(topic)
                print(f"已订阅主题: {topic}")
        elif rc == 5:
            print("认证失败: 用户名或密码错误")
        else:
            print(f"连接失败，错误代码: {rc}")
        
    # def on_message(self, client, userdata, msg):
    #     topic = msg.topic
    #     payload = msg.payload.decode()
        
    #     # 查找对应的处理函数
    #     for topic_config in self.topics:
    #         if topic_config["topic"] == topic:
    #             topic_config["handler"](topic, payload)
    #             return
        
    #     print(f"未找到主题 '{topic}' 的处理函数，收到消息: {payload}")

    def on_message(self, client, userdata, msg):
        print(f"📨 收到MQTT消息 - 主题: {msg.topic}")
        # 在事件循环中调度消息处理
        try:
            asyncio.run_coroutine_threadsafe(
                self.async_handle_message(msg.topic, msg.payload.decode()),
                self.loop
            )
        except Exception as e:
            print(f"❌ 处理消息时出错: {e}")

    
    async def async_handle_message(self, topic: str, payload: str):
        """异步处理消息"""
        if topic in self.topic_handlers:
            await self.topic_handlers[topic](payload)
    
    async def handle_theed(self, payload):
        print(f"🎯 进入 handle_theed 函数")
        print(f"📦 收到的payload: {payload[:200]}...")  # 只显示前200个字符

        enemy_units = self._parse_mqtt_data(payload)
        print(f"👹 敌方单位数量: {len(enemy_units)}")
        print("敌方单位：", enemy_units)

        friend_units = self.friendly_units
        print(f"👥 我方单位数量: {len(friend_units)}")
        print("我方单位：", friend_units)

        self.received_data["sys/esdk/status/8UUXN2Q00A01GE"] = enemy_units

        await self.check_and_send_combined_data()

    async def handle_osd(self, payload):
        self.received_data["thing/product/1581F8HGX255900A0DED/osd"] = payload
        await self.check_and_send_combined_data()
        print("无人机：", payload)
    
    async def check_and_send_combined_data(self):
        """检查是否两个主题的数据都已收到，如果是则发送合并数据"""
        print("🔍 检查合并数据条件...")

        # 检查两个主题的数据是否都已收到
        esdk_data = self.received_data["sys/esdk/status/8UUXN2Q00A01GE"]
        osd_data = self.received_data["thing/product/1581F8HGX255900A0DED/osd"]

        print(f"📊 ESDK数据状态: {'✅ 已收到' if esdk_data is not None else '❌ 未收到'}")
        print(f"📊 OSD数据状态: {'✅ 已收到' if osd_data is not None else '❌ 未收到'}")

        if esdk_data is not None and osd_data is not None:
            print("🎯 两个主题数据都已收到，开始合并...")

            # 创建合并数据
            # 将友方单位转换为可序列化的字典格式
            friendly_units_dict = []
            for unit in self.friendly_units:
                unit_dict = {
                    "id": unit.id,
                    "name": unit.name,
                    "type": unit.type,
                    "side": unit.side,
                    "position": {
                        "longitude": unit.position.longitude,
                        "latitude": unit.position.latitude,
                        "altitude": unit.position.altitude
                    },
                    "status": {
                        "health": unit.status.health,
                        "fuel": unit.status.fuel,
                        "operational": unit.status.operational,
                        "weapons": [
                            {
                                "weapon_type": weapon.weapon_type,
                                "quantity": weapon.quantity,
                                "max_quantity": weapon.max_quantity
                            } for weapon in unit.status.weapons
                        ]
                    },
                    "threat_level": unit.threat_level,
                    "confidence": unit.confidence,
                    "last_seen": unit.last_seen,
                    "speed": unit.speed,
                    "heading": unit.heading
                }
                friendly_units_dict.append(unit_dict)

            combined_data = {
                "enemy_units": esdk_data,
                "friendly_units": friendly_units_dict,
                "drone_data": osd_data,
                "timestamp": time.time()
            }

            # 发送合并数据
            print("📤 准备发送合并数据到API...")
            await self._send_threed_data_to_api(combined_data)

            # 重置数据，等待下一轮
            self.reset_received_data()
        else:
            print("⏳ 数据不完整，等待更多数据...")

    def reset_received_data(self):
        """重置接收到的数据"""
        self.received_data = {
            "sys/esdk/status/8UUXN2Q00A01GE": None,
            "thing/product/1581F8HGX255900A0DED/osd": None
        }
        print("🔄 数据已重置，等待下一轮数据...")

    async def _send_threed_data_to_api(self, data: Dict[str, Any]):
        """发送三维显示数据到API"""
        print(f"🚀 开始发送三维数据到API: {self.api_url}/threed/units")
        print(f"📦 数据大小: {len(str(data))} 字符")

        try:
            response = requests.post(
                f"{self.api_url}/threed/units",
                json=data,
                timeout=5
            )
            print(f"📡 API响应状态码: {response.status_code}")

            if response.status_code == 200:
                print(f"✅ 三维数据发送成功")
                logger.info(f"✅ 三维数据发送成功")
            else:
                print(f"❌ 三维数据发送失败 - 状态码: {response.status_code}")
                print(f"❌ 响应内容: {response.text}")
                logger.warning(f"❌ 三维数据发送失败 - 状态码: {response.status_code}, 响应: {response.text}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: 无法连接到API服务器 {self.api_url}")
            logger.error(f"❌ 连接错误: {e}")
        except requests.exceptions.Timeout as e:
            print(f"❌ 超时错误: API请求超时")
            logger.error(f"❌ 超时错误: {e}")
        except Exception as e:
            print(f"❌ 发送三维数据时发生未知错误: {e}")
            logger.error(f"❌ 发送三维数据时发生错误: {e}")
        
    def on_disconnect(self, client, userdata, rc):
        if rc != 0:
            print("意外断开连接")
        else:
            print("正常断开连接")
    
    def add_topic_handler(self, topic, handler):
        """动态添加主题和处理函数"""
        self.topic_handlers[topic] = handler
        if self.client.is_connected():
            self.client.subscribe(topic)
            print(f"新增订阅主题: {topic}")
        
    def start_in_thread(self):
        def run_mqtt():
            try:
                self.client.connect(self.broker, self.port, 60)
                self.client.loop_forever()
            except Exception as e:
                print(f"连接错误: {e}")

        def run_event_loop():
            asyncio.set_event_loop(self.loop)
            self.loop.run_forever()

        # 启动MQTT客户端线程
        mqtt_thread = threading.Thread(target=run_mqtt)
        mqtt_thread.daemon = True
        mqtt_thread.start()

        # 启动事件循环线程
        self.loop_thread = threading.Thread(target=run_event_loop)
        self.loop_thread.daemon = True
        self.loop_thread.start()
        
    def stop(self):
        self.client.disconnect()
        if self.loop.is_running():
            self.loop.call_soon_threadsafe(self.loop.stop)

    def _create_friendly_units(self) -> List[BattlefieldUnit]:
        """创建16个直-10我方单位 (ID: 0-15)，每架配置不同"""
        friendly_units = []

        # 手动创建16个不同配置的直-10单位
        units_data = [
            # 0号机
            BattlefieldUnit(
                id="0",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.0, 40.0, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 1号机
            BattlefieldUnit(
                id="1",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.001, 40.001, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 2号机
            BattlefieldUnit(
                id="2",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.002, 40.002, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 3号机
            BattlefieldUnit(
                id="3",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.003, 40.003, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 4号机
            BattlefieldUnit(
                id="4",
                name="直-10",
                type="武装直升机",
                side="我方",
            position=Position(116.004, 40.004, 1000.0),  # 您来补充具体位置
            status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
            threat_level="我方",
            confidence=1.0,
            last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            speed=0.0,  # 您来补充
            heading=0.0
        ),
            # 5号机
            BattlefieldUnit(
                id="5",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.005, 40.005, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 6号机
            BattlefieldUnit(
                id="6",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.006, 40.006, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 7号机
            BattlefieldUnit(
                id="7",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.007, 40.007, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 8号机
            BattlefieldUnit(
                id="8",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.008, 40.008, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 9号机
            BattlefieldUnit(
                id="9",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.009, 40.009, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 10号机
            BattlefieldUnit(
                id="10",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.010, 40.010, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 11号机
            BattlefieldUnit(
                id="11",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.011, 40.011, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 12号机
            BattlefieldUnit(
                id="12",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.012, 40.012, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 13号机
            BattlefieldUnit(
                id="13",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.013, 40.013, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 14号机
            BattlefieldUnit(
                id="14",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.014, 40.014, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            ),
            # 15号机
            BattlefieldUnit(
                id="15",
                name="直-10",
                type="武装直升机",
                side="我方",
                position=Position(116.015, 40.015, 1000.0),  # 您来补充具体位置
                status=UnitStatus(100.0, 100.0, True, []),  # health, fuel, operational, weapons
                threat_level="我方",
                confidence=1.0,
                last_seen=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                speed=0.0,  # 您来补充
                heading=0.0
            )
        ]

        friendly_units.extend(units_data)

        return friendly_units
    
    def _parse_mqtt_data(self, mqtt_payload: str) -> List[BattlefieldUnit]:
        """解析MQTT数据并转换为BattlefieldUnit列表"""
        try:
            data = json.loads(mqtt_payload)
            
            battlefield_units = []
            object_list = data.get('objectList', [])
            message_time = data.get('time', datetime.now().strftime('%Y-%m-%d %H:%M:%S'))
            
            for obj in object_list:
                try:
                    # 转换为BattlefieldUnit
                    unit = BattlefieldUnit(
                        id=str(obj.get('id', 'unknown')),
                        name=obj.get('class_name', 'unknown'),
                        type=obj.get('class_name', 'unknown'),
                        side="敌方",
                        position=Position(
                            longitude=obj.get('loc_longitude', 0.0),
                            latitude=obj.get('loc_latitude', 0.0),
                            altitude=obj.get('loc_height', 0.0)
                        ),
                        status=UnitStatus(
                            health=100.0,
                            fuel=85.0,
                            operational=True,
                            weapons=[]  # 敌方武器信息暂时为空，可后续补充
                        ),
                        threat_level="中等",
                        confidence=obj.get('confidence', 0.5),
                        last_seen=message_time,
                        speed=obj.get('speed', 0.0),
                        heading=0.0
                    )
                    battlefield_units.append(unit)
                                        
                except Exception as e:
                    continue

            return battlefield_units
            
        except Exception as e:
            return []

# 使用示例
if __name__ == "__main__":
    # 创建订阅者实例
    subscriber = AsyncMQTTSubscriber(
        broker="172.17.110.105",  # 替换为实际的MQTT broker地址
        port=1883,
        username="admin",  # 如果有认证，填写用户名
        password="tianjin@712"   # 如果有认证，填写密码
    )
    
    # 启动订阅
    subscriber.start_in_thread()
    
    # # 示例：动态添加第三个主题
    # def handle_pressure(payload):
    #     """处理气压数据"""
    #     try:
    #         pressure = float(payload)
    #         print(f"📊 气压数据: {pressure} hPa")
    #         if pressure < 1000:
    #             print("⚠️  气压偏低!")
    #     except ValueError:
    #         print(f"❌ 无效的气压数据: {payload}")
    
    # subscriber.add_topic_handler("sensor/pressure", handle_pressure)
    
    # print("MQTT订阅器已启动，按 Ctrl+C 停止...")
    
    # 主线程可以继续做其他事情
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("正在停止订阅者...")
        subscriber.stop()