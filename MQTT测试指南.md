# MQTT数据接收器测试指南

## 🎯 测试目标

验证 `mqtt_data_receiver.py` 的以下功能：
1. ✅ 正确解析您提供格式的MQTT数据
2. ✅ 转换为BattlefieldUnit格式
3. ✅ 创建战场数据包
4. ✅ 数据流控制器正常工作
5. ✅ 处理每秒15次的高频数据

## 📋 测试方案

### 方案1：直接功能测试（推荐，无需MQTT broker）

```bash
# 直接测试mqtt_data_receiver.py的所有功能
python test_mqtt_receiver.py
```

**优点**：
- 无需安装MQTT broker
- 快速验证所有功能
- 详细的测试报告
- 模拟真实数据格式

### 方案2：完整MQTT环境测试

```bash
# 1. 启动MQTT broker（需要安装mosquitto）
mosquitto -p 1883 -v

# 2. 新终端：启动MQTT数据模拟器
python mqtt_server_simulator.py

# 3. 新终端：启动MQTT接收器
python mqtt_data_receiver.py
```

## 🧪 测试内容详解

### 1. 数据解析测试
验证能否正确解析您提供的JSON格式：
```json
{
  "code": 0,
  "id": "-6",
  "seq": "26543322322",
  "time": "2025-9-12 12:34:21",
  "objectList": [
    {
      "class_name": "camera",
      "confidence": 0.67,
      "id": 124745,
      "loc_height": 557.7999877929688,
      "loc_latitude": 40.35440069941853,
      "loc_longitude": 115.91258306859129,
      "speed": 0,
      "url": "ocrByBox/2025-09/02--19-15-38-crop_124745-887268092.jpg"
    }
  ]
}
```

### 2. 数据转换测试
验证转换为BattlefieldUnit格式：
- ✅ ID映射正确
- ✅ 位置信息准确
- ✅ 威胁等级评估
- ✅ 时间戳处理

### 3. 高频数据处理测试
模拟每秒15次数据发送：
- ✅ 数据缓冲机制
- ✅ 优先级处理
- ✅ AI分析调度（30秒间隔）
- ✅ 前端更新控制（1秒间隔）

### 4. 数据流控制器测试
验证智能调度功能：
- ✅ 缓冲区管理
- ✅ 优先级评估
- ✅ 异步任务调度
- ✅ 统计监控

## 🚀 快速开始

### 步骤1：运行功能测试
```bash
python test_mqtt_receiver.py
```

**预期输出**：
```
🚀 启动MQTT接收器功能测试
🧪 测试数据解析功能...
📝 测试 正常场景
✅ 解析成功 - 解析出 2 个敌方单位
   示例单位: camera (ID: 124745)
   位置: (115.912583, 40.354401)
   威胁等级: 中等, 置信度: 0.67

🧪 测试战场数据包创建功能...
✅ 战场数据包创建成功
   时间戳: 2025-01-XX...
   敌方单位数: 2
   战场状态: {'total_enemy': 2, 'total_friendly': 0, 'active_threats': 0}

🧪 测试数据流控制器集成...
📤 第 1 批数据已提交到流控制器
...
✅ 数据流控制器集成测试完成

🧪 测试高频数据处理能力 - 持续 30 秒...
✅ 高频数据处理测试完成
   处理消息: 450
   实际频率: 15.0Hz
   目标频率: 15Hz

📊 测试总结
   总测试数: 4
   成功测试: 4
   失败测试: 0
   成功率: 100.0%
```

### 步骤2：验证与FastAPI集成（可选）

如果您的 `main_fastapi.py` 可以运行：

```bash
# 终端1：启动FastAPI
python main_fastapi.py

# 终端2：运行测试（会尝试发送数据到FastAPI）
python test_mqtt_receiver.py
```

## 📊 测试数据格式

测试脚本会生成符合您格式的数据：

```json
{
  "code": 0,
  "id": "-6",
  "seq": "26543322322", 
  "time": "2025-01-XX XX:XX:XX",
  "objectList": [
    {
      "class_name": "camera|vehicle|tank|aircraft|missile|radar",
      "confidence": 0.6-0.95,
      "height": -1000到100,
      "id": 124745,
      "loc_height": 100-800,
      "loc_latitude": 40.354±0.01,
      "loc_longitude": 115.912±0.01,
      "speed": 0-80,
      "url": "ocrByBox/时间戳-crop_ID-随机数.jpg",
      "width": -1200到100
    }
  ]
}
```

## 🔧 故障排除

### 问题1：导入错误
```
ModuleNotFoundError: No module named 'xxx'
```
**解决**：确保所有依赖文件在同一目录：
- `mqtt_data_receiver.py`
- `data_flow_controller.py`
- `battlefield_simulator.py`

### 问题2：配置文件错误
```
配置加载失败
```
**解决**：检查 `config/mqtt_config.json` 文件存在且格式正确

### 问题3：API连接失败
```
❌ 发送数据失败: Connection refused
```
**解决**：这是正常的，因为测试时FastAPI可能未运行。测试脚本会继续验证其他功能。

## 📈 性能指标

### 预期性能：
- **数据解析速度**：>1000条/秒
- **内存使用**：<100MB（缓冲100条记录）
- **处理延迟**：<10ms（单条数据）
- **高频处理**：稳定15Hz输入

### 实际测试结果：
运行测试脚本后会显示实际性能数据。

## 🎉 测试成功标准

✅ **所有测试通过**：成功率100%
✅ **数据解析正确**：能解析您的JSON格式
✅ **高频处理稳定**：15Hz输入无数据丢失
✅ **数据流控制正常**：AI分析和前端更新按预期调度
✅ **无内存泄漏**：长时间运行内存稳定

## 📝 下一步

测试通过后，您可以：
1. 配置真实的MQTT服务器地址
2. 启动完整系统进行生产测试
3. 根据实际需求调整数据流控制器参数

## 💡 提示

- 测试脚本会生成详细日志，便于调试
- 可以修改测试参数（如持续时间、频率）
- 支持不同场景测试（正常、活跃、关键）
