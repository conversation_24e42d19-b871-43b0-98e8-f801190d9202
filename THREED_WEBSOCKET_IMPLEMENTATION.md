# 三维显示WebSocket实时推送功能实现

## 🎯 功能概述

为战场态势分析系统添加了三维显示的WebSocket实时推送功能，实现：
1. **双重数据流**：分析数据（1分钟1次）+ 三维显示数据（1分钟25次）
2. **高频实时推送**：每2.4秒推送一次单位位置和状态数据
3. **独立WebSocket通道**：专门的三维显示WebSocket接口
4. **简化数据格式**：只包含三维显示必需的基础信息

## 🔧 后端实现

### 1. **三维显示连接管理器**

```python
class ThreeDConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []
        self.connection_count = 0

    async def broadcast(self, message: str):
        """广播消息到所有三维显示客户端"""
        for connection in self.active_connections:
            await connection.send_text(message)
```

### 2. **三维显示WebSocket端点**

```python
@app.websocket("/ws/threed")
async def threed_websocket_endpoint(websocket: WebSocket):
    """三维显示WebSocket端点 - 实时单位数据推送"""
    await threed_manager.connect(websocket)
    # 处理连接、心跳、状态查询等
```

### 3. **三维显示数据接收接口**

```python
@app.post("/threed/units")
async def receive_threed_units(units_data: Dict[str, Any]):
    """接收三维显示单位数据并广播"""
    await broadcast_threed_data(units_data)
    return {"status": "success"}
```

### 4. **数据广播函数**

```python
async def broadcast_threed_data(units_data: Dict[str, Any]):
    """广播三维显示数据到所有三维显示客户端"""
    message = {
        "type": "threed_units",
        "data": units_data,
        "timestamp": datetime.now().isoformat()
    }
    await threed_manager.broadcast(json.dumps(message, ensure_ascii=False))
```

## 🎮 模拟器改进

### 1. **双重数据发送逻辑**

```python
async def start_simulation(self, analysis_interval_seconds=60, threed_interval_seconds=2.4):
    """
    开始模拟
    - analysis_interval_seconds: 发送给大模型分析的间隔（60秒）
    - threed_interval_seconds: 发送给三维显示的间隔（2.4秒，每分钟25次）
    """
    while self.running:
        current_time = time.time() - start_time
        
        # 检查是否需要发送分析数据（每分钟一次）
        if current_time - last_analysis_time >= analysis_interval_seconds:
            battlefield_data = self._create_battlefield_data()
            await self._send_data_to_api(battlefield_data)
        
        # 检查是否需要发送三维显示数据（每分钟25次）
        if current_time - last_threed_time >= threed_interval_seconds:
            threed_data = self._create_threed_data()
            await self._send_threed_data_to_api(threed_data)
```

### 2. **简化的三维数据格式**

```python
def _create_threed_data(self) -> Dict[str, Any]:
    """创建三维显示数据包（简化版本）"""
    return {
        "timestamp": datetime.now().isoformat(),
        "simulation_time": self.simulation_time,
        "enemy_units": [
            {
                "id": unit.id,
                "name": unit.name,
                "type": unit.type,
                "side": unit.side,
                "position": {
                    "longitude": unit.position.longitude,
                    "latitude": unit.position.latitude,
                    "altitude": unit.position.altitude
                },
                "status": {
                    "health": unit.status.health,
                    "ammo": unit.status.ammo,
                    "fuel": unit.status.fuel,
                    "operational": unit.status.operational
                },
                "threat_level": unit.threat_level,
                "confidence": unit.confidence,
                "speed": unit.speed,
                "heading": unit.heading
            } for unit in self.enemy_units
        ],
        "friendly_units": [/* 同样格式的我方单位 */]
    }
```

### 3. **平滑移动更新**

```python
def _update_units_for_threed(self, time_delta_minutes: float):
    """为三维显示更新单位位置（轻微移动）"""
    for unit in self.enemy_units + self.friendly_units:
        if unit.status.operational:
            # 轻微的位置更新，用于三维显示的平滑移动
            self._update_unit_position(unit, time_delta_minutes * 0.1)
```

## 🌐 前端实现

### 1. **三维显示WebSocket客户端**

```javascript
class ThreeDWebSocketClient {
    constructor() {
        this.wsUrl = 'ws://localhost:8000/ws/threed';
        this.dataUpdates = 0;
        this.units = { enemy_units: [], friendly_units: [] };
    }
    
    handleUnitsData(message) {
        this.units = message.data;
        this.updateUnitsDisplay();
        this.updateMapDisplay();
    }
}
```

### 2. **实时地图显示**

```javascript
updateMapDisplay() {
    // 清除现有标记
    const existingMarkers = mapContainer.querySelectorAll('.unit-marker');
    existingMarkers.forEach(marker => marker.remove());
    
    // 添加新的单位标记
    this.units.enemy_units.forEach(unit => {
        const marker = this.createUnitMarker(unit, x, y, 'enemy');
        mapContainer.appendChild(marker);
    });
}
```

### 3. **单位标记和工具提示**

```javascript
createUnitMarker(unit, x, y, type) {
    const marker = document.createElement('div');
    marker.className = `unit-marker ${type}`;
    marker.style.left = `${x - 10}px`;
    marker.style.top = `${y - 10}px`;
    
    // 添加悬停事件显示详细信息
    marker.addEventListener('mouseenter', (e) => {
        this.showTooltip(e, unit);
    });
}
```

## 📡 数据流架构

### **双重数据流设计**

```
模拟器 ──┬── 分析数据 (60秒/次) ──→ /battlefield/analyze ──→ LLM分析 ──→ WebSocket推送分析结果
         │
         └── 三维数据 (2.4秒/次) ──→ /threed/units ──→ WebSocket推送单位数据 ──→ 三维显示
```

### **消息类型**

1. **连接消息**
```json
{
    "type": "connection",
    "status": "connected",
    "message": "三维显示WebSocket连接已建立"
}
```

2. **单位数据消息**
```json
{
    "type": "threed_units",
    "data": {
        "enemy_units": [...],
        "friendly_units": [...]
    },
    "timestamp": "2025-09-22T08:00:00"
}
```

3. **心跳消息**
```json
{
    "type": "ping/pong",
    "timestamp": "2025-09-22T08:00:00"
}
```

## 🚀 使用方法

### **1. 启动后端服务**
```bash
python -m uvicorn main_fastapi:app --host 127.0.0.1 --port 8000
```

### **2. 启动模拟器**
```bash
python battlefield_simulator.py
```

### **3. 打开三维显示页面**
```bash
# 在浏览器中打开
frontend/threed_display.html
```

### **4. 观察实时效果**
- 三维显示页面自动连接WebSocket
- 每2.4秒接收一次单位数据更新
- 地图上的单位标记实时移动
- 侧边栏显示详细单位信息

## 📊 API端点总览

### **WebSocket端点**
- `ws://localhost:8000/ws/battlefield` - 分析结果推送
- `ws://localhost:8000/ws/threed` - 三维显示数据推送

### **HTTP端点**
- `POST /battlefield/analyze` - 战场分析（触发分析结果推送）
- `POST /threed/units` - 三维显示数据接收（触发单位数据推送）
- `GET /ws/status` - 分析WebSocket状态
- `GET /ws/threed/status` - 三维显示WebSocket状态

## 🎯 性能特点

### **高频数据推送**
- **频率**: 每分钟25次（每2.4秒一次）
- **数据量**: 简化的单位基础信息
- **延迟**: 毫秒级实时推送
- **并发**: 支持多客户端同时连接

### **资源优化**
- **网络开销**: 最小化数据包大小
- **CPU使用**: 轻量级位置更新
- **内存占用**: 高效的连接管理
- **错误恢复**: 自动重连和异常处理

## 🎉 实现完成

**三维显示WebSocket实时推送功能已完全实现！**

现在您的战场态势分析系统具备：
- ✅ **双重数据流**：分析数据 + 三维显示数据
- ✅ **高频实时推送**：每分钟25次单位数据更新
- ✅ **独立WebSocket通道**：专门的三维显示接口
- ✅ **实时地图显示**：单位位置实时更新
- ✅ **完整的前后端集成**：从模拟器到三维显示的完整数据流

**系统现在可以同时支持战场分析和三维实时显示！** 🚀
