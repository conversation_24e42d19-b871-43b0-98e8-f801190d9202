{"status": "success", "analysis_id": "ANALYSIS_1759133643571", "timestamp": "2025-09-29T16:14:33.800064", "processing_time_ms": 30228.1, "battlefield_summary": {"analysis_id": "ANALYSIS_1759133643571", "timestamp": "2025-09-29T16:14:33.799977", "simulation_time": 166.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 0, "operational_friendly": 0, "high_threats": 2, "processing_time_ms": 30228.1}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击敌方T-90主战坦克", "决策建议": "派遣A组直升机机群（id为0、1、2、3）执行此次任务。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式，针对敌方T-90主战坦克进行精确打击。同时，C组直升机机群（id为8、9、10、11）作为预备队，待命支援或转移至其他目标区域。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2：集中打击敌方BTR-80装甲运兵车", "决策建议": "派遣B组直升机机群（id为4、5、6、7）执行此次任务。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式，针对敌方BTR-80装甲运兵车进行打击。同时，D组直升机机群（id为12、13、14、15）作为预备队，准备应对可能出现的敌方增援或转移至其他目标区域。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案3：分散打击，兼顾T-90和BTR-80", "决策建议": "A组直升机机群（id为0、1、2、3）和B组直升机机群（id为4、5、6、7）分别负责打击敌方T-90主战坦克和BTR-80装甲运兵车。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式。C组和D组直升机机群作为预备队，随时准备支援或转移至其他目标区域。", "毁伤概率": "85%（针对每个目标）", "任务时限": "预计40分钟"}, {"方案名称": "方案4：优先应对敌方苏-35战斗机威胁", "决策建议": "鉴于敌方苏-35战斗机的威胁等级极高，建议立即调动我方歼-20战斗机（尽管目前不可作战，但应尽快恢复其作战能力）。同时，A组和B组直升机机群（id为0、1、2、3和4、5、6、7）携带70mm火箭弹，采用密集射击方式，对敌方地面目标进行压制性打击，以分散敌方注意力并争取时间。C组和D组直升机机群作为预备队，待命支援或转移至其他目标区域。", "毁伤概率": "无法计算（需根据歼-20恢复情况决定）", "任务时限": "预计45分钟"}, {"方案名称": "方案5：综合打击，优先摧毁敌方T-90主战坦克，同时压制BTR-80装甲运兵车", "决策建议": "A组直升机机群（id为0、1、2、3）负责打击敌方T-90主战坦克，每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式。B组直升机机群（id为4、5、6、7）负责压制敌方BTR-80装甲运兵车，每架直升机携带70mm火箭弹，采用密集射击方式。C组和D组直升机机群作为预备队，随时准备支援或转移至其他目标区域。", "毁伤概率": "85%（针对T-90），无法计算（针对BTR-80，取决于密集射击效果）", "任务时限": "预计40分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击敌方T-90主战坦克\",\n      \"决策建议\": \"派遣A组直升机机群（id为0、1、2、3）执行此次任务。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式，针对敌方T-90主战坦克进行精确打击。同时，C组直升机机群（id为8、9、10、11）作为预备队，待命支援或转移至其他目标区域。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：集中打击敌方BTR-80装甲运兵车\",\n      \"决策建议\": \"派遣B组直升机机群（id为4、5、6、7）执行此次任务。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式，针对敌方BTR-80装甲运兵车进行打击。同时，D组直升机机群（id为12、13、14、15）作为预备队，准备应对可能出现的敌方增援或转移至其他目标区域。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：分散打击，兼顾T-90和BTR-80\",\n      \"决策建议\": \"A组直升机机群（id为0、1、2、3）和B组直升机机群（id为4、5、6、7）分别负责打击敌方T-90主战坦克和BTR-80装甲运兵车。每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式。C组和D组直升机机群作为预备队，随时准备支援或转移至其他目标区域。\",\n      \"毁伤概率\": \"85%（针对每个目标）\",\n      \"任务时限\": \"预计40分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：优先应对敌方苏-35战斗机威胁\",\n      \"决策建议\": \"鉴于敌方苏-35战斗机的威胁等级极高，建议立即调动我方歼-20战斗机（尽管目前不可作战，但应尽快恢复其作战能力）。同时，A组和B组直升机机群（id为0、1、2、3和4、5、6、7）携带70mm火箭弹，采用密集射击方式，对敌方地面目标进行压制性打击，以分散敌方注意力并争取时间。C组和D组直升机机群作为预备队，待命支援或转移至其他目标区域。\",\n      \"毁伤概率\": \"无法计算（需根据歼-20恢复情况决定）\",\n      \"任务时限\": \"预计45分钟\"\n    },\n    {\n      \"方案名称\": \"方案5：综合打击，优先摧毁敌方T-90主战坦克，同时压制BTR-80装甲运兵车\",\n      \"决策建议\": \"A组直升机机群（id为0、1、2、3）负责打击敌方T-90主战坦克，每架直升机携带2枚AKD10空地导弹，采用双击双发协同攻击方式。B组直升机机群（id为4、5、6、7）负责压制敌方BTR-80装甲运兵车，每架直升机携带70mm火箭弹，采用密集射击方式。C组和D组直升机机群作为预备队，随时准备支援或转移至其他目标区域。\",\n      \"毁伤概率\": \"85%（针对T-90），无法计算（针对BTR-80，取决于密集射击效果）\",\n      \"任务时限\": \"预计40分钟\"\n    }\n  ]\n}\n``` \n\n### 解析说明：\n1. **方案1和方案2**：针对单一高威胁目标（T-90主战坦克或BTR-80装甲运兵车）进行集中打击，采用双击双发协同攻击方式，确保较高的毁伤概率。\n2. **方案3**：分散打击，同时处理两个高威胁目标，确保资源分配合理，但需要更长的任务时限。\n3. **方案4**：优先应对敌方苏-35战斗机的威胁，强调空中力量的恢复和地面压制，适合在敌方空中威胁较大的情况下使用。\n4. **方案5**：综合打击，优先摧毁高威胁目标（T-90主战坦克），同时通过压制手段削弱中威胁目标（BTR-80装甲运兵车）的战斗力，是一种较为均衡的选择。\n\n根据战场态势，建议优先选择**方案1**或**方案3**，因为T-90主战坦克的威胁等级最高，且AKD10空地导弹能够有效应对这类目标。如果敌方苏-35战斗机构成严重威胁，则应优先考虑**方案4**，尽快恢复我方歼-20战斗机的作战能力。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 3173, "processing_time": 30076.53, "timestamp": **********.7996383}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.26}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 29.65}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 28.79}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 28.36}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.6}]}}