{"status": "success", "analysis_id": "ANALYSIS_1759110172469", "timestamp": "2025-09-29T09:43:18.291168", "processing_time_ms": 25821.73, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110172469", "timestamp": "2025-09-29T09:43:18.291089", "simulation_time": 7.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 25821.73}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击T-90主战坦克", "决策建议": "出动A组直升机机群（编号0-3），采用双击双发协同攻击模式，每架直升机携带2枚AKD10空地导弹，集中攻击T-90主战坦克。同时，部署歼-20战斗机在高空警戒，防止苏-35战斗机干扰。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2：分散打击敌方装甲车辆", "决策建议": "出动B组直升机机群（编号4-7），采用单发发射模式，每架直升机携带1枚AKD10空地导弹，分别攻击T-90主战坦克和BTR-80装甲运兵车。同时，安排C组直升机机群（编号8-11）携带70mm火箭弹，对BTR-80装甲运兵车进行补充打击。", "毁伤概率": "70%", "任务时限": "预计25分钟"}, {"方案名称": "方案3：空中掩护与地面打击结合", "决策建议": "出动D组直升机机群（编号12-15），采用双击双发协同攻击模式，每架直升机携带2枚AKD10空地导弹，集中攻击T-90主战坦克。同时，歼-20战斗机负责空中掩护，利用其隐身能力和超视距攻击优势，对苏-35战斗机进行压制。", "毁伤概率": "85%", "任务时限": "预计40分钟"}, {"方案名称": "方案4：重点摧毁苏-35战斗机威胁", "决策建议": "优先派遣歼-20战斗机，利用其隐身能力和先进航电系统，对苏-35战斗机实施超视距攻击。同时，安排A组和B组直升机机群（编号0-7）对地面目标（T-90主战坦克和BTR-80装甲运兵车）进行打击。", "毁伤概率": "90%", "任务时限": "预计45分钟"}, {"方案名称": "方案5：综合打击与防御部署", "决策建议": "出动A组和C组直升机机群（编号0-3和8-11），采用双击双发协同攻击模式，集中攻击T-90主战坦克。同时，D组直升机机群（编号12-15）携带70mm火箭弹，对BTR-80装甲运兵车进行补充打击。歼-20战斗机负责空中掩护，确保直升机机群的安全。", "毁伤概率": "80%", "任务时限": "预计30分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机机群（编号0-3），采用双击双发协同攻击模式，每架直升机携带2枚AKD10空地导弹，集中攻击T-90主战坦克。同时，部署歼-20战斗机在高空警戒，防止苏-35战斗机干扰。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：分散打击敌方装甲车辆\",\n      \"决策建议\": \"出动B组直升机机群（编号4-7），采用单发发射模式，每架直升机携带1枚AKD10空地导弹，分别攻击T-90主战坦克和BTR-80装甲运兵车。同时，安排C组直升机机群（编号8-11）携带70mm火箭弹，对BTR-80装甲运兵车进行补充打击。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：空中掩护与地面打击结合\",\n      \"决策建议\": \"出动D组直升机机群（编号12-15），采用双击双发协同攻击模式，每架直升机携带2枚AKD10空地导弹，集中攻击T-90主战坦克。同时，歼-20战斗机负责空中掩护，利用其隐身能力和超视距攻击优势，对苏-35战斗机进行压制。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计40分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：重点摧毁苏-35战斗机威胁\",\n      \"决策建议\": \"优先派遣歼-20战斗机，利用其隐身能力和先进航电系统，对苏-35战斗机实施超视距攻击。同时，安排A组和B组直升机机群（编号0-7）对地面目标（T-90主战坦克和BTR-80装甲运兵车）进行打击。\",\n      \"毁伤概率\": \"90%\",\n      \"任务时限\": \"预计45分钟\"\n    },\n    {\n      \"方案名称\": \"方案5：综合打击与防御部署\",\n      \"决策建议\": \"出动A组和C组直升机机群（编号0-3和8-11），采用双击双发协同攻击模式，集中攻击T-90主战坦克。同时，D组直升机机群（编号12-15）携带70mm火箭弹，对BTR-80装甲运兵车进行补充打击。歼-20战斗机负责空中掩护，确保直升机机群的安全。\",\n      \"毁伤概率\": \"80%\",\n      \"任务时限\": \"预计30分钟\"\n    }\n  ]\n}\n``` \n\n### 分析说明：\n1. **方案1**：优先针对T-90主战坦克，因为其威胁等级最高且防护性能强，需要集中火力进行精准打击。双击双发协同攻击模式能够提高命中率，确保有效摧毁目标。\n   \n2. **方案2**：分散打击敌方装甲车辆，通过不同机群的配合，分别针对T-90主战坦克和BTR-80装甲运兵车，降低单一目标的压力，同时利用火箭弹对装甲运兵车进行补充打击。\n\n3. **方案3**：空中掩护与地面打击结合，利用歼-20战斗机的隐身能力和超视距攻击优势，确保直升机机群的安全，同时集中火力摧毁T-90主战坦克。\n\n4. **方案4**：重点摧毁苏-35战斗机威胁，首先解决空中威胁，再利用直升机机群对地面目标进行打击，确保整体作战安全。\n\n5. **方案5**：综合打击与防御部署，集中力量打击T-90主战坦克，同时利用火箭弹对BTR-80装甲运兵车进行补充打击，歼-20战斗机负责空中掩护，确保整体作战效果。\n\n每个方案都充分考虑了敌我双方的力量对比、直升机的作战半径、导弹的命中率以及战场环境等因素，提供了多种灵活的作战选择。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 3029, "processing_time": 25670.42, "timestamp": **********.2907677}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.06}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 30.21}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 28.51}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 28.33}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.61}]}}