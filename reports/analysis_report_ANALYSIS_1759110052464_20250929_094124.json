{"status": "success", "analysis_id": "ANALYSIS_1759110052464", "timestamp": "2025-09-29T09:41:24.818834", "processing_time_ms": 32354.63, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110052464", "timestamp": "2025-09-29T09:41:24.818753", "simulation_time": 3.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 32354.63}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击敌方T-90主战坦克", "决策建议": "出动A组直升机（id为0、1、2、3）执行对地攻击任务，采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，安排歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克保持待命状态，准备应对可能的地面反击。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2：集中火力打击敌方BTR-80装甲运兵车", "决策建议": "出动B组直升机（id为4、5、6、7）执行对地攻击任务，采用快速发射模式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。同时，安排歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克保持待命状态，准备应对可能的地面反击。", "毁伤概率": "70%", "任务时限": "预计25分钟"}, {"方案名称": "方案3：歼-20战斗机优先打击敌方苏-35战斗机", "决策建议": "歼-20战斗机立即调整航向，利用隐身能力和超音速巡航性能，对敌方苏-35战斗机实施超视距攻击。同时，A组直升机（id为0、1、2、3）待命，准备在敌方苏-35被击落后，迅速对地面目标实施攻击。", "毁伤概率": "90%", "任务时限": "预计20分钟"}, {"方案名称": "方案4：综合打击，优先消灭威胁最高的敌方目标", "决策建议": "首先，歼-20战斗机对敌方苏-35战斗机实施超视距攻击，利用隐身优势优先将其击落。随后，A组直升机（id为0、1、2、3）对敌方T-90主战坦克实施双击双发协同攻击，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。最后，B组直升机（id为4、5、6、7）对敌方BTR-80装甲运兵车实施快速发射攻击，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。", "毁伤概率": "85%", "任务时限": "预计50分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击敌方T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机（id为0、1、2、3）执行对地攻击任务，采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，安排歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克保持待命状态，准备应对可能的地面反击。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：集中火力打击敌方BTR-80装甲运兵车\",\n      \"决策建议\": \"出动B组直升机（id为4、5、6、7）执行对地攻击任务，采用快速发射模式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。同时，安排歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克保持待命状态，准备应对可能的地面反击。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：歼-20战斗机优先打击敌方苏-35战斗机\",\n      \"决策建议\": \"歼-20战斗机立即调整航向，利用隐身能力和超音速巡航性能，对敌方苏-35战斗机实施超视距攻击。同时，A组直升机（id为0、1、2、3）待命，准备在敌方苏-35被击落后，迅速对地面目标实施攻击。\",\n      \"毁伤概率\": \"90%\",\n      \"任务时限\": \"预计20分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：综合打击，优先消灭威胁最高的敌方目标\",\n      \"决策建议\": \"首先，歼-20战斗机对敌方苏-35战斗机实施超视距攻击，利用隐身优势优先将其击落。随后，A组直升机（id为0、1、2、3）对敌方T-90主战坦克实施双击双发协同攻击，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。最后，B组直升机（id为4、5、6、7）对敌方BTR-80装甲运兵车实施快速发射攻击，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计50分钟\"\n    }\n  ]\n}\n``` \n\n### 方案解析：\n1. **方案1：优先打击敌方T-90主战坦克**\n   - **原因**：T-90主战坦克威胁等级最高，且其防护和火力都非常强大，需要集中火力予以摧毁。\n   - **策略**：采用双击双发协同攻击方式，可以显著提高命中率，确保摧毁目标。同时，歼-20战斗机负责空中掩护，防止敌方苏-35战斗机干扰直升机行动。\n   - **毁伤概率**：双击双发协同攻击命中率为85%，满足有效攻击标准。\n\n2. **方案2：集中火力打击敌方BTR-80装甲运兵车**\n   - **原因**：BTR-80装甲运兵车威胁等级中等，但其高机动性和两栖能力使其成为潜在威胁。快速发射模式可以迅速覆盖多个目标。\n   - **策略**：每架直升机发射1枚导弹，快速发射模式适合对付这类移动目标。歼-20战斗机同样负责空中掩护。\n   - **毁伤概率**：单机单发命中率为70%，符合最低有效攻击标准。\n\n3. **方案3：歼-20战斗机优先打击敌方苏-35战斗机**\n   - **原因**：敌方苏-35战斗机威胁等级极高，对我方直升机和地面部队构成严重威胁。优先消灭敌方空中力量可以大幅降低后续作战风险。\n   - **策略**：歼-20利用隐身能力和超视距攻击优势，迅速锁定并摧毁敌方苏-35战斗机。直升机待命，确保在空中威胁解除后迅速投入战斗。\n   - **毁伤概率**：歼-20的火力评级为10/10，结合隐身优势，预计命中率可达90%。\n\n4. **方案4：综合打击，优先消灭威胁最高的敌方目标**\n   - **原因**：综合考虑敌方所有威胁，优先解决空中威胁（苏-35战斗机），然后依次打击地面高威胁目标（T-90主战坦克）和中威胁目标（BTR-80装甲运兵车）。\n   - **策略**：歼-20先解决空中威胁，A组直升机随后集中火力摧毁T-90主战坦克，最后B组直升机清理BTR-80装甲运兵车。\n   - **毁伤概率**：整体毁伤概率由关键步骤决定，即歼-20对苏-35的攻击命中率（90%）和A组直升机对T-90的协同攻击命中率（85%）。\n\n通过上述方案，可以有效应对当前战场态势，合理分配资源，最大化打击效果，同时确保我方部队的安全。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 3271, "processing_time": 32206.34, "timestamp": **********.8184333}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 30.47}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.62}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 28.42}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 28.13}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.11}]}}