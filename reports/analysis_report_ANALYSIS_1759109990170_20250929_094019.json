{"status": "success", "analysis_id": "ANALYSIS_1759109990170", "timestamp": "2025-09-29T09:40:19.448041", "processing_time_ms": 29277.73, "battlefield_summary": {"analysis_id": "ANALYSIS_1759109990170", "timestamp": "2025-09-29T09:40:19.447954", "simulation_time": 1.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 29277.73}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击敌方T-90主战坦克", "决策建议": "出动A组直升机（id为0、1、2、3）执行对地攻击任务，采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，歼-20战斗机负责空中掩护，防止苏-35战斗机干扰。99A主战坦克保持待命状态，随时准备应对可能的地面反击。", "毁伤概率": "85%", "任务时限": "预计40分钟"}, {"方案名称": "方案2：集中火力摧毁敌方装甲运兵车", "决策建议": "出动B组直升机（id为4、5、6、7）执行对地攻击任务，采用单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。同时，A组直升机（id为0、1、2、3）携带70mm火箭弹，对装甲运兵车周围区域进行火力覆盖，以清除潜在威胁。歼-20战斗机继续执行空中掩护任务。", "毁伤概率": "70%", "任务时限": "预计35分钟"}, {"方案名称": "方案3：空中打击与地面配合，全向压制敌方", "决策建议": "出动C组直升机（id为8、9、10、11）和D组直升机（id为12、13、14、15）分别执行对地攻击任务。C组直升机采用双击双发协同攻击方式，对T-90主战坦克实施精确打击，D组直升机采用单发发射方式，对装甲运兵车进行打击。同时，99A主战坦克在地面待命，随时准备支援或反击。歼-20战斗机负责全程空中掩护，必要时对苏-35战斗机进行拦截。", "毁伤概率": "80%", "任务时限": "预计50分钟"}, {"方案名称": "方案4：歼-20优先打击敌方苏-35战斗机", "决策建议": "歼-20战斗机利用隐身能力和超视距攻击优势，优先对苏-35战斗机实施远程打击，确保空中优势。随后，A组直升机（id为0、1、2、3）和B组直升机（id为4、5、6、7）分别对T-90主战坦克和装甲运兵车进行对地攻击，采用双击双发协同攻击方式，确保高毁伤概率。", "毁伤概率": "85%", "任务时限": "预计45分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击敌方T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机（id为0、1、2、3）执行对地攻击任务，采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，歼-20战斗机负责空中掩护，防止苏-35战斗机干扰。99A主战坦克保持待命状态，随时准备应对可能的地面反击。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计40分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：集中火力摧毁敌方装甲运兵车\",\n      \"决策建议\": \"出动B组直升机（id为4、5、6、7）执行对地攻击任务，采用单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。同时，A组直升机（id为0、1、2、3）携带70mm火箭弹，对装甲运兵车周围区域进行火力覆盖，以清除潜在威胁。歼-20战斗机继续执行空中掩护任务。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：空中打击与地面配合，全向压制敌方\",\n      \"决策建议\": \"出动C组直升机（id为8、9、10、11）和D组直升机（id为12、13、14、15）分别执行对地攻击任务。C组直升机采用双击双发协同攻击方式，对T-90主战坦克实施精确打击，D组直升机采用单发发射方式，对装甲运兵车进行打击。同时，99A主战坦克在地面待命，随时准备支援或反击。歼-20战斗机负责全程空中掩护，必要时对苏-35战斗机进行拦截。\",\n      \"毁伤概率\": \"80%\",\n      \"任务时限\": \"预计50分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：歼-20优先打击敌方苏-35战斗机\",\n      \"决策建议\": \"歼-20战斗机利用隐身能力和超视距攻击优势，优先对苏-35战斗机实施远程打击，确保空中优势。随后，A组直升机（id为0、1、2、3）和B组直升机（id为4、5、6、7）分别对T-90主战坦克和装甲运兵车进行对地攻击，采用双击双发协同攻击方式，确保高毁伤概率。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计45分钟\"\n    }\n  ]\n}\n``` \n\n### 分析说明：\n1. **敌方威胁评估**：\n   - **T-90主战坦克**：威胁等级高，防护和火力强，需要重点打击。\n   - **BTR-80装甲运兵车**：威胁等级中，装甲较弱，但机动性强，需结合火力覆盖。\n   - **苏-35战斗机**：威胁等级极高，对我方直升机和地面部队构成严重威胁，必须优先处理。\n\n2. **我方资源分配**：\n   - **歼-20战斗机**：隐身性能和超视距攻击能力使其成为对抗苏-35的关键力量。\n   - **99A主战坦克**：作为地面威慑力量，目前无武器，暂时不参与直接攻击。\n   - **直升机机群**：分为四组，每组4架，可根据任务需求灵活调配，主要承担对地攻击任务。\n\n3. **战术选择**：\n   - **双击双发协同攻击**：针对高价值目标（如T-90主战坦克），采用此方式可提高命中率至85%，但会延长任务时间。\n   - **单发发射**：适用于中低威胁目标（如装甲运兵车），命中率70%，任务效率较高。\n   - **70mm火箭弹**：用于辅助清理装甲运兵车周边区域，弥补精确打击不足。\n\n4. **任务时限与毁伤概率**：\n   - 各方案根据目标威胁等级和打击方式计算了毁伤概率和任务时限，确保行动的可行性与有效性。\n\n5. **综合策略**：\n   - 优先处理空中威胁（苏-35战斗机），确保直升机和地面部队的安全。\n   - 针对地面高价值目标（T-90主战坦克）采用高毁伤概率的攻击方式。\n   - 结合多种武器系统（AKD10导弹、70mm火箭弹）实现全方位打击。\n\n通过上述方案，可以有效应对当前战场态势，最大化发挥我方装备优势，同时降低风险。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 3137, "processing_time": 29047.02, "timestamp": **********.447631}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 66.1}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 30.06}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 29.78}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 29.78}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.92}]}}