{"status": "success", "analysis_id": "ANALYSIS_1759110201001", "timestamp": "2025-09-29T09:43:45.094437", "processing_time_ms": 24093.13, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110201001", "timestamp": "2025-09-29T09:43:45.094361", "simulation_time": 8.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 24093.13}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击敌方T-90主战坦克", "决策建议": "出动A组直升机（id为0、1、2、3），采取双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，安排歼-20战斗机负责空中掩护，防止苏-35战斗机干扰。99A主战坦克保持待命状态，准备应对可能的地面反击。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2：集中打击敌方装甲运兵车", "决策建议": "出动B组直升机（id为4、5、6、7），采取单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。由于装甲运兵车装甲较薄，单发命中率已足够。同时，安排99A主战坦克在附近区域待命，准备清理残余威胁。歼-20战斗机继续执行空中掩护任务。", "毁伤概率": "70%", "任务时限": "预计25分钟"}, {"方案名称": "方案3：优先摧毁敌方战斗机威胁", "决策建议": "出动歼-20战斗机，利用其隐身能力和超视距攻击优势，优先打击敌方苏-35战斗机。同时，安排C组直升机（id为8、9、10、11）待命，准备后续地面打击任务。99A主战坦克保持警戒状态，防止敌方地面部队反击。", "毁伤概率": "90%", "任务时限": "预计40分钟"}, {"方案名称": "方案4：综合打击，优先处理高威胁目标", "决策建议": "首先，出动歼-20战斗机，利用其隐身能力和超视距攻击优势，优先打击敌方苏-35战斗机，确保空中优势。其次，出动A组直升机（id为0、1、2、3），采取双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹，集中打击敌方T-90主战坦克。最后，根据剩余情况，出动B组直升机（id为4、5、6、7），采取单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹，打击敌方装甲运兵车。99A主战坦克在整个过程中保持警戒状态，随时准备应对地面反击。", "毁伤概率": "85%", "任务时限": "预计55分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击敌方T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机（id为0、1、2、3），采取双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。同时，安排歼-20战斗机负责空中掩护，防止苏-35战斗机干扰。99A主战坦克保持待命状态，准备应对可能的地面反击。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：集中打击敌方装甲运兵车\",\n      \"决策建议\": \"出动B组直升机（id为4、5、6、7），采取单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹。由于装甲运兵车装甲较薄，单发命中率已足够。同时，安排99A主战坦克在附近区域待命，准备清理残余威胁。歼-20战斗机继续执行空中掩护任务。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：优先摧毁敌方战斗机威胁\",\n      \"决策建议\": \"出动歼-20战斗机，利用其隐身能力和超视距攻击优势，优先打击敌方苏-35战斗机。同时，安排C组直升机（id为8、9、10、11）待命，准备后续地面打击任务。99A主战坦克保持警戒状态，防止敌方地面部队反击。\",\n      \"毁伤概率\": \"90%\",\n      \"任务时限\": \"预计40分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：综合打击，优先处理高威胁目标\",\n      \"决策建议\": \"首先，出动歼-20战斗机，利用其隐身能力和超视距攻击优势，优先打击敌方苏-35战斗机，确保空中优势。其次，出动A组直升机（id为0、1、2、3），采取双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹，集中打击敌方T-90主战坦克。最后，根据剩余情况，出动B组直升机（id为4、5、6、7），采取单发发射方式，每架直升机发射1枚AKD10空地导弹，共计4枚导弹，打击敌方装甲运兵车。99A主战坦克在整个过程中保持警戒状态，随时准备应对地面反击。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计55分钟\"\n    }\n  ]\n}\n``` \n\n### 分析说明：\n1. **方案1**：优先打击敌方T-90主战坦克，因为其威胁等级最高且防护性能强，需要较高的毁伤概率来确保任务成功。采用双击双发协同攻击方式可以显著提高命中率，但会延长任务时限。\n   \n2. **方案2**：集中打击敌方装甲运兵车，因其威胁等级较低且装甲较薄，单发命中率已足够，因此选择单发发射方式以节省资源并缩短任务时间。\n\n3. **方案3**：优先摧毁敌方战斗机威胁，因为苏-35战斗机的威胁等级极高，如果不先消除空中威胁，后续地面行动将面临较大风险。歼-20战斗机凭借其隐身能力和超视距攻击能力，能够有效完成这一任务。\n\n4. **方案4**：综合打击方案，优先处理高威胁目标（苏-35战斗机和T-90主战坦克），确保我方空中和地面优势，再根据实际情况处理低威胁目标（装甲运兵车）。这种方案较为全面，但任务时限较长。\n\n通过上述方案，结合敌我双方的战场态势和装备特点，能够有效应对不同威胁，实现作战目标。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 2965, "processing_time": 23942.8, "timestamp": **********.0940726}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.06}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.69}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 28.6}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 29.06}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.38}]}}