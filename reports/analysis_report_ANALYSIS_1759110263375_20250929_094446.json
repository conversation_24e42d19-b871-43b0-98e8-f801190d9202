{"status": "success", "analysis_id": "ANALYSIS_1759110263375", "timestamp": "2025-09-29T09:44:46.009028", "processing_time_ms": 22633.08, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110263375", "timestamp": "2025-09-29T09:44:46.008951", "simulation_time": 10.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 22633.08}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1 - 空地协同打击T-90主战坦克", "决策建议": "出动A组直升机机群（id: 0-3），采用双击双发协同攻击方式，每架直升机携带2枚AKD10空地导弹，共计8枚导弹。同时，歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克在地面待命，准备应对可能的敌方反击。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2 - 区域封锁与逐个击破", "决策建议": "出动B组直升机机群（id: 4-7），使用单发发射模式，每架直升机携带1枚AKD10空地导弹，优先打击BTR-80装甲运兵车（威胁等级中）。同时，A组直升机机群（id: 0-3）继续执行方案1的任务。歼-20战斗机全程提供空中掩护，防止苏-35战斗机干扰。", "毁伤概率": "70%", "任务时限": "预计25分钟"}, {"方案名称": "方案3 - 制空权争夺与远程打击", "决策建议": "歼-20战斗机携带空空导弹，主动出击，优先摧毁苏-35战斗机。同时，D组直升机机群（id: 12-15）携带AKD10空地导弹，对T-90主战坦克实施远程打击，采用双击双发协同攻击方式。99A主战坦克在后方待命，随时准备支援。", "毁伤概率": "85%", "任务时限": "预计40分钟"}, {"方案名称": "方案4 - 全面压制与后续清理", "决策建议": "C组直升机机群（id: 8-11）携带70mm火箭弹，对整个战场区域进行大面积火力覆盖，重点针对BTR-80装甲运兵车和可能的敌方步兵。其他直升机机群根据实际情况轮换执行方案1和方案3的任务。歼-20战斗机保持警戒，确保制空权。", "毁伤概率": "约70%-80%（取决于具体目标分布）", "任务时限": "预计30分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1 - 空地协同打击T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机机群（id: 0-3），采用双击双发协同攻击方式，每架直升机携带2枚AKD10空地导弹，共计8枚导弹。同时，歼-20战斗机负责空中掩护，确保直升机安全。99A主战坦克在地面待命，准备应对可能的敌方反击。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2 - 区域封锁与逐个击破\",\n      \"决策建议\": \"出动B组直升机机群（id: 4-7），使用单发发射模式，每架直升机携带1枚AKD10空地导弹，优先打击BTR-80装甲运兵车（威胁等级中）。同时，A组直升机机群（id: 0-3）继续执行方案1的任务。歼-20战斗机全程提供空中掩护，防止苏-35战斗机干扰。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案3 - 制空权争夺与远程打击\",\n      \"决策建议\": \"歼-20战斗机携带空空导弹，主动出击，优先摧毁苏-35战斗机。同时，D组直升机机群（id: 12-15）携带AKD10空地导弹，对T-90主战坦克实施远程打击，采用双击双发协同攻击方式。99A主战坦克在后方待命，随时准备支援。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计40分钟\"\n    },\n    {\n      \"方案名称\": \"方案4 - 全面压制与后续清理\",\n      \"决策建议\": \"C组直升机机群（id: 8-11）携带70mm火箭弹，对整个战场区域进行大面积火力覆盖，重点针对BTR-80装甲运兵车和可能的敌方步兵。其他直升机机群根据实际情况轮换执行方案1和方案3的任务。歼-20战斗机保持警戒，确保制空权。\",\n      \"毁伤概率\": \"约70%-80%（取决于具体目标分布）\",\n      \"任务时限\": \"预计30分钟\"\n    }\n  ]\n}\n``` \n\n### 解析说明：\n1. **方案1**：针对T-90主战坦克的高威胁等级，采用双击双发协同攻击方式，以提高命中率至85%，确保能够有效摧毁目标。歼-20战斗机提供空中掩护，防止敌方战斗机干扰。\n   \n2. **方案2**：针对BTR-80装甲运兵车的中威胁等级，采用单发发射模式，快速打击并削弱敌方机动能力。同时，A组直升机继续执行方案1的任务，确保资源分配合理。\n\n3. **方案3**：优先解决苏-35战斗机的极高压制威胁，由歼-20战斗机负责制空权争夺。随后，D组直升机对T-90主战坦克进行远程打击，继续保持双击双发协同攻击，确保高毁伤概率。\n\n4. **方案4**：利用C组直升机的70mm火箭弹进行大面积火力覆盖，清理战场上的中小型目标（如BTR-80装甲运兵车和步兵），为后续行动创造有利条件。同时，其他直升机机群根据实际情况灵活调整任务。\n\n通过上述方案，可以实现对不同威胁等级目标的有效打击，同时确保我方兵力的安全和作战效率。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 2909, "processing_time": 22480.08, "timestamp": **********.0086536}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.12}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.85}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 29.22}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 29.72}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 29.09}]}}