{"status": "success", "analysis_id": "ANALYSIS_1759110351316", "timestamp": "2025-09-29T09:46:13.779027", "processing_time_ms": 22462.08, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110351316", "timestamp": "2025-09-29T09:46:13.778951", "simulation_time": 13.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 22462.08}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1 - 空中打击优先，集中火力摧毁T-90主战坦克", "决策建议": "出动A组直升机（id: 0、1、2、3），采用双击双发协同攻击模式，每架直升机发射2枚AKD10空地导弹，针对T-90主战坦克实施精确打击。同时，安排歼-20战斗机执行空中掩护任务，确保直升机安全。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2 - 制空权争夺，歼-20与苏-35空中对决", "决策建议": "歼-20战斗机立即前往苏-35战斗机所在空域，利用隐身能力和超视距攻击优势，尝试在远距离锁定并摧毁苏-35。同时，地面部队保持警戒，防止敌方战机对我方地面单位发动攻击。", "毁伤概率": "75%", "任务时限": "预计20分钟"}, {"方案名称": "方案3 - 多轴联动，综合打击敌方装甲运兵车", "决策建议": "出动B组直升机（id: 4、5、6、7），采用单发发射模式，每架直升机发射1枚AKD10空地导弹，针对BTR-80装甲运兵车进行精准打击。同时，99A主战坦克做好准备，随时支援地面作战。", "毁伤概率": "70%", "任务时限": "预计25分钟"}, {"方案名称": "方案4 - 综合防御，应对敌方可能的反击", "决策建议": "99A主战坦克占据有利地形，构筑防御阵地，准备应对敌方可能的地面反击。同时，安排部分直升机携带70mm火箭弹，作为预备队，随时准备支援或打击敌方增援力量。", "毁伤概率": "65%", "任务时限": "预计40分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1 - 空中打击优先，集中火力摧毁T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机（id: 0、1、2、3），采用双击双发协同攻击模式，每架直升机发射2枚AKD10空地导弹，针对T-90主战坦克实施精确打击。同时，安排歼-20战斗机执行空中掩护任务，确保直升机安全。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2 - 制空权争夺，歼-20与苏-35空中对决\",\n      \"决策建议\": \"歼-20战斗机立即前往苏-35战斗机所在空域，利用隐身能力和超视距攻击优势，尝试在远距离锁定并摧毁苏-35。同时，地面部队保持警戒，防止敌方战机对我方地面单位发动攻击。\",\n      \"毁伤概率\": \"75%\",\n      \"任务时限\": \"预计20分钟\"\n    },\n    {\n      \"方案名称\": \"方案3 - 多轴联动，综合打击敌方装甲运兵车\",\n      \"决策建议\": \"出动B组直升机（id: 4、5、6、7），采用单发发射模式，每架直升机发射1枚AKD10空地导弹，针对BTR-80装甲运兵车进行精准打击。同时，99A主战坦克做好准备，随时支援地面作战。\",\n      \"毁伤概率\": \"70%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案4 - 综合防御，应对敌方可能的反击\",\n      \"决策建议\": \"99A主战坦克占据有利地形，构筑防御阵地，准备应对敌方可能的地面反击。同时，安排部分直升机携带70mm火箭弹，作为预备队，随时准备支援或打击敌方增援力量。\",\n      \"毁伤概率\": \"65%\",\n      \"任务时限\": \"预计40分钟\"\n    }\n  ]\n}\n``` \n\n### 分析说明：\n1. **方案1 - 空中打击优先，集中火力摧毁T-90主战坦克**  \n   - T-90主战坦克威胁等级高，且装甲厚实，需要集中火力才能有效摧毁。选择A组直升机采用双击双发协同攻击模式，可以最大化命中率（85%）。同时，歼-20战斗机负责空中掩护，确保直升机安全。\n   \n2. **方案2 - 制空权争夺，歼-20与苏-35空中对决**  \n   - 苏-35战斗机威胁等级极高，必须优先解决制空权问题。歼-20凭借隐身能力和超视距攻击优势，可以在远距离锁定并攻击苏-35，降低其对我方地面部队的威胁。\n\n3. **方案3 - 多轴联动，综合打击敌方装甲运兵车**  \n   - BTR-80装甲运兵车威胁等级中等，但高机动性和两栖能力使其难以捕捉。选择B组直升机采用单发发射模式，针对性地打击装甲运兵车，同时地面部队做好配合。\n\n4. **方案4 - 综合防御，应对敌方可能的反击**  \n   - 99A主战坦克作为地面主力，需要构筑防御阵地，防止敌方反击。同时，安排直升机携带70mm火箭弹作为预备队，随时准备支援或打击敌方增援力量，确保整体防御体系的完整性。\n\n通过上述方案，可以有效应对不同类型的威胁，合理分配资源，确保作战任务的成功完成。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 2920, "processing_time": 22311.11, "timestamp": **********.7786667}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.83}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.89}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 28.4}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 28.74}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.47}]}}