{"status": "success", "analysis_id": "ANALYSIS_1759110084821", "timestamp": "2025-09-29T09:41:53.078292", "processing_time_ms": 28256.71, "battlefield_summary": {"analysis_id": "ANALYSIS_1759110084821", "timestamp": "2025-09-29T09:41:53.078175", "simulation_time": 4.0, "enemy_count": 3, "friendly_count": 2, "operational_enemy": 3, "operational_friendly": 2, "high_threats": 2, "processing_time_ms": 28256.71}, "threat_assessment": {}, "tactical_recommendation": {"战术建议": {}, "作战方案": [{"方案名称": "方案1：优先打击敌方T-90主战坦克", "决策建议": "出动A组直升机机群（id为0、1、2、3），采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。首先由A组直升机在敌方T-90主战坦克附近建立空中掩护，同时利用激光照射装置锁定目标。由于T-90主战坦克的装甲厚实，需要集中火力以提高命中率和毁伤效果。", "毁伤概率": "85%", "任务时限": "预计35分钟"}, {"方案名称": "方案2：歼-20战斗机优先应对苏-35战斗机威胁", "决策建议": "我方歼-20战斗机立即调整航向，利用其隐身能力和超视距攻击优势，迅速接近并锁定敌方苏-35战斗机。使用远程空空导弹实施超视距攻击，争取在敌方苏-35战斗机进入有效攻击范围之前将其击落。同时，直升机机群保持高度戒备，避免暴露在敌方战斗机的攻击范围内。", "毁伤概率": "90%", "任务时限": "预计20分钟"}, {"方案名称": "方案3：B组直升机机群打击敌方BTR-80装甲运兵车", "决策建议": "出动B组直升机机群（id为4、5、6、7），每架直升机发射2枚AKD10空地导弹，共计8枚导弹，采取单发发射模式。鉴于BTR-80装甲运兵车的装甲较薄且机动性强，选择单发发射可以更快地覆盖多个目标，同时减少任务完成时间。直升机应从不同方向接近，以降低被发现的风险。", "毁伤概率": "80%", "任务时限": "预计25分钟"}, {"方案名称": "方案4：联合打击，先歼灭苏-35再清理地面目标", "决策建议": "首先，由歼-20战斗机负责清除敌方苏-35战斗机的威胁，确保空中优势。随后，A组直升机机群（id为0、1、2、3）和B组直升机机群（id为4、5、6、7）分别对T-90主战坦克和BTR-80装甲运兵车实施联合打击。A组采用双击双发协同攻击，B组采用单发发射模式。同时，C组直升机机群（id为8、9、10、11）作为预备队，随时准备支援或执行其他任务。", "毁伤概率": "88%", "任务时限": "预计50分钟"}, {"方案名称": "方案5：99A主战坦克配合直升机机群行动", "决策建议": "我方99A主战坦克保持静默，隐蔽待命，同时通过雷达和传感器收集敌方地面目标的情报信息。一旦直升机机群完成对敌方地面目标的首轮打击，99A主战坦克迅速推进至敌方T-90主战坦克和BTR-80装甲运兵车残骸附近，利用其强大的火力和防护能力，彻底摧毁残存的目标。同时，直升机机群继续提供空中掩护。", "毁伤概率": "92%", "任务时限": "预计60分钟"}], "应急预案": {}}, "llm_analysis": {"status": "success", "analysis_result": "```json\n{\n  \"作战方案\": [\n    {\n      \"方案名称\": \"方案1：优先打击敌方T-90主战坦克\",\n      \"决策建议\": \"出动A组直升机机群（id为0、1、2、3），采用双击双发协同攻击方式，每架直升机发射2枚AKD10空地导弹，共计8枚导弹。首先由A组直升机在敌方T-90主战坦克附近建立空中掩护，同时利用激光照射装置锁定目标。由于T-90主战坦克的装甲厚实，需要集中火力以提高命中率和毁伤效果。\",\n      \"毁伤概率\": \"85%\",\n      \"任务时限\": \"预计35分钟\"\n    },\n    {\n      \"方案名称\": \"方案2：歼-20战斗机优先应对苏-35战斗机威胁\",\n      \"决策建议\": \"我方歼-20战斗机立即调整航向，利用其隐身能力和超视距攻击优势，迅速接近并锁定敌方苏-35战斗机。使用远程空空导弹实施超视距攻击，争取在敌方苏-35战斗机进入有效攻击范围之前将其击落。同时，直升机机群保持高度戒备，避免暴露在敌方战斗机的攻击范围内。\",\n      \"毁伤概率\": \"90%\",\n      \"任务时限\": \"预计20分钟\"\n    },\n    {\n      \"方案名称\": \"方案3：B组直升机机群打击敌方BTR-80装甲运兵车\",\n      \"决策建议\": \"出动B组直升机机群（id为4、5、6、7），每架直升机发射2枚AKD10空地导弹，共计8枚导弹，采取单发发射模式。鉴于BTR-80装甲运兵车的装甲较薄且机动性强，选择单发发射可以更快地覆盖多个目标，同时减少任务完成时间。直升机应从不同方向接近，以降低被发现的风险。\",\n      \"毁伤概率\": \"80%\",\n      \"任务时限\": \"预计25分钟\"\n    },\n    {\n      \"方案名称\": \"方案4：联合打击，先歼灭苏-35再清理地面目标\",\n      \"决策建议\": \"首先，由歼-20战斗机负责清除敌方苏-35战斗机的威胁，确保空中优势。随后，A组直升机机群（id为0、1、2、3）和B组直升机机群（id为4、5、6、7）分别对T-90主战坦克和BTR-80装甲运兵车实施联合打击。A组采用双击双发协同攻击，B组采用单发发射模式。同时，C组直升机机群（id为8、9、10、11）作为预备队，随时准备支援或执行其他任务。\",\n      \"毁伤概率\": \"88%\",\n      \"任务时限\": \"预计50分钟\"\n    },\n    {\n      \"方案名称\": \"方案5：99A主战坦克配合直升机机群行动\",\n      \"决策建议\": \"我方99A主战坦克保持静默，隐蔽待命，同时通过雷达和传感器收集敌方地面目标的情报信息。一旦直升机机群完成对敌方地面目标的首轮打击，99A主战坦克迅速推进至敌方T-90主战坦克和BTR-80装甲运兵车残骸附近，利用其强大的火力和防护能力，彻底摧毁残存的目标。同时，直升机机群继续提供空中掩护。\",\n      \"毁伤概率\": \"92%\",\n      \"任务时限\": \"预计60分钟\"\n    }\n  ]\n}\n``` \n\n### 分析说明：\n1. **方案1**：针对敌方T-90主战坦克的高威胁等级，采用双击双发协同攻击方式，能够显著提升命中率和毁伤效果，但需要更多的时间来完成任务。\n   \n2. **方案2**：歼-20战斗机优先应对苏-35战斗机的威胁，确保我方在空中占据主动权，避免直升机机群受到敌方战斗机的干扰。\n\n3. **方案3**：针对BTR-80装甲运兵车的中等威胁等级，采用单发发射模式，快速覆盖多个目标，同时降低任务完成时间。\n\n4. **方案4**：联合打击方案，充分发挥歼-20战斗机的空中优势和直升机机群的地面打击能力，确保全面压制敌方地面和空中威胁。\n\n5. **方案5**：结合99A主战坦克的地面优势，确保在直升机机群完成首轮打击后，进一步摧毁残存目标，实现彻底胜利。\n\n这些方案综合考虑了敌我双方的兵力配置、装备性能以及战场环境，旨在最大化战斗效率和生存率。", "llm_provider": "openai_compatible", "model": "Qwen2.5-72B-Instruct-GPTQ-Int4", "tokens_used": 3118, "processing_time": 28099.2, "timestamp": **********.077851}, "weapon_knowledge": {"enemy_knowledge": [{"result": {"id": 1, "weapon_name": "T-90主战坦克", "weapon_type": "主战坦克", "country": "俄罗斯", "manufacturer": "乌拉尔车辆厂", "max_range": 4.0, "max_speed": 60.0, "armor_thickness": 850.0, "main_weapon": "125mm滑膛炮", "crew_count": 3, "weight": 46.5, "firepower_rating": 9, "protection_rating": 9, "mobility_rating": 7, "detection_rating": 6, "terrain_adaptability": {"urban": 8, "desert": 9, "forest": 6, "mountain": 5}, "weather_limitations": {"rain": 7, "snow": 6, "fog": 5}, "advantages": "强大的火力，优秀的防护，先进的火控系统", "weaknesses": "机动性相对较低，燃料消耗大，维护复杂", "effective_against": ["装甲车", "步兵战车", "反坦克炮"], "vulnerable_to": ["攻击直升机", "反坦克导弹", "空中打击"], "recommended_tactics": "集群作战，火力压制，装甲突击", "counter_tactics": "空中打击，侧翼包围，反坦克导弹伏击", "technical_specs": {"armor": "复合装甲+反应装甲", "engine": "V-92S2柴油机", "transmission": "自动变速箱"}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 31.15}, {"result": {"id": 2, "weapon_name": "BTR-80装甲运兵车", "weapon_type": "装甲车", "country": "俄罗斯", "manufacturer": "高尔基汽车厂", "max_range": 2.0, "max_speed": 80.0, "armor_thickness": 300.0, "main_weapon": "14.5mm机枪", "crew_count": 10, "weight": 13.6, "firepower_rating": 5, "protection_rating": 6, "mobility_rating": 9, "detection_rating": 4, "terrain_adaptability": {"urban": 9, "desert": 8, "forest": 7, "mountain": 6}, "weather_limitations": {"rain": 8, "snow": 7, "fog": 6}, "advantages": "高机动性，两栖能力，载员能力强", "weaknesses": "装甲较薄，火力有限，易受重武器攻击", "effective_against": ["步兵", "轻型车辆", "工事"], "vulnerable_to": ["主战坦克", "反坦克武器", "重机枪"], "recommended_tactics": "快速机动，步兵输送，火力支援", "counter_tactics": "重武器打击，地雷阻击，狙击手攻击", "technical_specs": {"engine": "KAMAZ-7403柴油机", "capacity": "7名步兵", "amphibious": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.93}, {"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 苏-35战斗机(战斗机)"}, "processing_time_ms": 34.15}], "friendly_knowledge": [{"result": {"status": "not_found", "weapon_data": null, "message": "武器知识库中未找到 99A主战坦克(主战坦克)"}, "processing_time_ms": 29.35}, {"result": {"id": 3, "weapon_name": "歼-20战斗机", "weapon_type": "战斗机", "country": "中国", "manufacturer": "成都飞机工业集团", "max_range": 2000.0, "max_speed": 2100.0, "armor_thickness": 0.0, "main_weapon": "航炮+导弹", "crew_count": 1, "weight": 19.0, "firepower_rating": 10, "protection_rating": 8, "mobility_rating": 10, "detection_rating": 10, "terrain_adaptability": {"all_terrain": 10}, "weather_limitations": {"storm": 3, "fog": 4}, "advantages": "隐身能力强，超音速巡航，先进航电", "weaknesses": "维护成本高，对基础设施要求高", "effective_against": ["战斗机", "轰炸机", "地面目标"], "vulnerable_to": ["防空导弹", "电子干扰", "隐身战机"], "recommended_tactics": "超视距攻击，隐身突防，制空作战", "counter_tactics": "防空网拦截，电子对抗，多层防御", "technical_specs": {"engine": "WS-15涡扇发动机", "stealth": true, "supercruise": true}, "created_at": "2025-09-17T14:36:24", "updated_at": "2025-09-17T14:36:24"}, "processing_time_ms": 28.34}]}}