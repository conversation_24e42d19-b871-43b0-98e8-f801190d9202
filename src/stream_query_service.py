"""
流式查询服务模块 - 处理图像识别结果的实时查询
"""
import json
import time
from typing import Dict, Any, List, Optional, Union
from .query_service import QueryService
import logging


class StreamQueryService:
    """流式查询服务类 - 专门处理图像识别结果的实时查询"""
    
    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化流式查询服务
        
        Args:
            config_file: 配置文件路径
        """
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化基础查询服务
        self.query_service = QueryService(config_file)
        
        # 流式查询统计
        self.stats = {
            "total_requests": 0,
            "successful_queries": 0,
            "empty_results": 0,
            "error_count": 0,
            "start_time": time.time()
        }
    
    def parse_image_recognition_result(self, recognition_data: Union[str, Dict]) -> Dict[str, Any]:
        """
        解析图像识别结果
        
        Args:
            recognition_data: 图像识别结果数据
            
        Returns:
            标准化的识别结果
        """
        try:
            if isinstance(recognition_data, str):
                data = json.loads(recognition_data)
            else:
                data = recognition_data
            
            # 标准化识别结果格式
            result = {
                "image_id": data.get("image_id", f"img_{int(time.time())}"),
                "timestamp": data.get("timestamp", time.time()),
                "confidence": data.get("confidence", 0.0),
                "detected_objects": data.get("detected_objects", []),
                "status": data.get("status", "unknown")
            }
            
            return result
            
        except Exception as e:
            self.logger.error(f"图像识别结果解析失败: {str(e)}")
            raise ValueError(f"无效的图像识别结果格式: {str(e)}")
    
    def extract_query_names(self, detected_objects: List[Dict]) -> List[str]:
        """
        从检测到的对象中提取查询名称
        
        Args:
            detected_objects: 检测到的对象列表
            
        Returns:
            查询名称列表
        """
        names = []
        
        for obj in detected_objects:
            # 提取对象名称
            name = obj.get("name") or obj.get("label") or obj.get("class_name")
            confidence = obj.get("confidence", 0.0)
            
            # 只处理置信度足够高的识别结果
            if name and confidence >= 0.5:  # 可配置的置信度阈值
                names.append(name)
        
        # 去重但保持顺序
        unique_names = []
        for name in names:
            if name not in unique_names:
                unique_names.append(name)
        
        return unique_names
    
    def stream_query(self, recognition_data: Union[str, Dict]) -> Dict[str, Any]:
        """
        流式查询主方法 - 处理图像识别结果并查询数据库
        
        Args:
            recognition_data: 图像识别结果
            
        Returns:
            流式查询结果
        """
        start_time = time.time()
        self.stats["total_requests"] += 1
        
        try:
            # 解析图像识别结果
            parsed_data = self.parse_image_recognition_result(recognition_data)
            
            # 提取查询名称
            query_names = self.extract_query_names(parsed_data["detected_objects"])
            
            # 构建查询结果
            result = {
                "stream_id": f"stream_{int(time.time() * 1000)}",
                "image_id": parsed_data["image_id"],
                "timestamp": parsed_data["timestamp"],
                "processing_time": 0,  # 将在最后计算
                "recognition_status": parsed_data["status"],
                "detected_count": len(parsed_data["detected_objects"]),
                "query_count": len(query_names),
                "query_results": None,
                "status": "processing"
            }
            
            # 根据识别结果数量选择查询策略
            if not query_names:
                # 情况1: 空结果
                result.update({
                    "status": "empty",
                    "message": "未识别到任何可查询的对象",
                    "query_results": {
                        "found": {},
                        "not_found": [],
                        "total_requested": 0,
                        "total_found": 0
                    }
                })
                self.stats["empty_results"] += 1
                
            elif len(query_names) == 1:
                # 情况2: 单个对象
                query_result = self.query_service.query_by_name({"name": query_names[0]})
                
                if query_result["status"] == "success":
                    result.update({
                        "status": "success",
                        "message": f"成功查询到 1 个对象的信息",
                        "query_results": {
                            "found": {query_names[0]: query_result["data"]},
                            "not_found": [],
                            "total_requested": 1,
                            "total_found": 1
                        }
                    })
                    self.stats["successful_queries"] += 1
                else:
                    result.update({
                        "status": "not_found",
                        "message": f"未找到对象 '{query_names[0]}' 的信息",
                        "query_results": {
                            "found": {},
                            "not_found": query_names,
                            "total_requested": 1,
                            "total_found": 0
                        }
                    })
                
            else:
                # 情况3: 多个对象
                query_result = self.query_service.query_by_names({"names": query_names})
                
                result.update({
                    "status": query_result["status"],
                    "message": f"批量查询完成，识别到 {len(query_names)} 个对象",
                    "query_results": query_result["data"]
                })
                
                if query_result["data"]["total_found"] > 0:
                    self.stats["successful_queries"] += 1
            
            # 计算处理时间
            result["processing_time"] = round((time.time() - start_time) * 1000, 2)  # 毫秒
            
            self.logger.info(f"流式查询完成 - 图像ID: {result['image_id']}, "
                           f"识别数量: {result['detected_count']}, "
                           f"查询数量: {result['query_count']}, "
                           f"处理时间: {result['processing_time']}ms")
            
            return result
            
        except Exception as e:
            self.stats["error_count"] += 1
            self.logger.error(f"流式查询失败: {str(e)}")
            
            return {
                "stream_id": f"stream_{int(time.time() * 1000)}",
                "image_id": "unknown",
                "timestamp": time.time(),
                "processing_time": round((time.time() - start_time) * 1000, 2),
                "status": "error",
                "message": f"流式查询失败: {str(e)}",
                "query_results": None,
                "detected_count": 0,
                "query_count": 0
            }
    
    def batch_stream_query(self, recognition_batch: List[Union[str, Dict]]) -> List[Dict[str, Any]]:
        """
        批量流式查询 - 处理多个图像识别结果
        
        Args:
            recognition_batch: 图像识别结果批次
            
        Returns:
            批量查询结果列表
        """
        results = []
        batch_start_time = time.time()
        
        self.logger.info(f"开始批量流式查询 - 批次大小: {len(recognition_batch)}")
        
        for i, recognition_data in enumerate(recognition_batch):
            try:
                result = self.stream_query(recognition_data)
                result["batch_index"] = i
                results.append(result)
                
            except Exception as e:
                self.logger.error(f"批次中第 {i} 个查询失败: {str(e)}")
                results.append({
                    "batch_index": i,
                    "status": "error",
                    "message": f"查询失败: {str(e)}",
                    "processing_time": 0
                })
        
        batch_processing_time = round((time.time() - batch_start_time) * 1000, 2)
        
        self.logger.info(f"批量流式查询完成 - 处理时间: {batch_processing_time}ms, "
                        f"成功: {sum(1 for r in results if r['status'] in ['success', 'empty', 'not_found'])}, "
                        f"失败: {sum(1 for r in results if r['status'] == 'error')}")
        
        return results
    
    def get_stream_stats(self) -> Dict[str, Any]:
        """
        获取流式查询统计信息
        
        Returns:
            统计信息字典
        """
        current_time = time.time()
        uptime = current_time - self.stats["start_time"]
        
        return {
            "uptime_seconds": round(uptime, 2),
            "total_requests": self.stats["total_requests"],
            "successful_queries": self.stats["successful_queries"],
            "empty_results": self.stats["empty_results"],
            "error_count": self.stats["error_count"],
            "success_rate": round(
                (self.stats["successful_queries"] / max(self.stats["total_requests"], 1)) * 100, 2
            ),
            "requests_per_second": round(
                self.stats["total_requests"] / max(uptime, 1), 2
            )
        }
    
    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            "total_requests": 0,
            "successful_queries": 0,
            "empty_results": 0,
            "error_count": 0,
            "start_time": time.time()
        }
