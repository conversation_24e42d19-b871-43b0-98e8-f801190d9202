"""
战场数据流式处理服务
处理实时战场JSON数据，进行武器知识库查询
"""
import json
import time
from typing import Dict, Any, List, Optional, Union
from decimal import Decimal
from datetime import datetime
from .database_connection import DatabaseConnection
import logging


class BattlefieldDataService:
    """战场数据流式处理服务类"""
    
    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化战场数据服务
        
        Args:
            config_file: 数据库配置文件路径
        """
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库连接
        self.db_connection = DatabaseConnection(config_file)
        
        # 处理统计
        self.stats = {
            "total_processed": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "start_time": time.time()
        }
        
        self.logger.info("战场数据流式处理服务初始化完成")

    def _convert_decimal_to_float(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """将字典中的Decimal类型转换为float类型，datetime类型转换为字符串"""
        if not data:
            return data

        converted_data = {}
        for key, value in data.items():
            if isinstance(value, Decimal):
                converted_data[key] = float(value)
            elif isinstance(value, datetime):
                # 将datetime对象转换为ISO格式字符串
                converted_data[key] = value.isoformat()
            elif isinstance(value, dict):
                converted_data[key] = self._convert_decimal_to_float(value)
            elif isinstance(value, list):
                converted_data[key] = [
                    self._convert_decimal_to_float(item) if isinstance(item, dict)
                    else float(item) if isinstance(item, Decimal)
                    else item.isoformat() if isinstance(item, datetime)
                    else item
                    for item in value
                ]
            else:
                converted_data[key] = value
        return converted_data
    
    def parse_battlefield_json(self, json_data: Union[str, Dict]) -> Dict[str, Any]:
        """
        解析战场JSON数据
        
        Args:
            json_data: 战场JSON数据
            
        Returns:
            解析后的标准化数据
        """
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data
            
            # 标准化字段名称（处理中文字段）
            standardized_data = {
                "id": data.get("ID") or data.get("id"),
                "weapon_name": data.get("武器名称") or data.get("weapon_name"),
                "weapon_type": data.get("武器类型") or data.get("weapon_type"),
                "longitude": data.get("经度") or data.get("longitude"),  # 注意：精度应该是经度
                "latitude": data.get("纬度") or data.get("latitude"),
                "altitude": data.get("高度") or data.get("altitude"),
                "faction": data.get("属方") or data.get("faction"),
                "timestamp": data.get("timestamp", time.time()),
                "raw_data": data  # 保留原始数据
            }
            
            # 验证必要字段
            required_fields = ["weapon_name", "weapon_type"]
            for field in required_fields:
                if not standardized_data.get(field):
                    raise ValueError(f"缺少必要字段: {field}")
            
            return standardized_data
            
        except Exception as e:
            self.logger.error(f"战场JSON数据解析失败: {str(e)}")
            raise ValueError(f"无效的战场JSON数据: {str(e)}")
    
    def query_weapon_knowledge(self, weapon_name: str, weapon_type: str) -> Dict[str, Any]:
        """
        查询武器知识库信息
        
        Args:
            weapon_name: 武器名称
            weapon_type: 武器类型
            
        Returns:
            武器知识库信息
        """
        try:
            # 构建查询SQL - 优先精确匹配，然后模糊匹配
            query_sql = """
            SELECT * FROM weapon_knowledge 
            WHERE (weapon_name = %s AND weapon_type = %s)
               OR (weapon_name LIKE %s AND weapon_type = %s)
               OR (weapon_name = %s AND weapon_type LIKE %s)
               OR (weapon_name LIKE %s AND weapon_type LIKE %s)
            ORDER BY 
                CASE 
                    WHEN weapon_name = %s AND weapon_type = %s THEN 1
                    WHEN weapon_name LIKE %s AND weapon_type = %s THEN 2
                    WHEN weapon_name = %s AND weapon_type LIKE %s THEN 3
                    ELSE 4
                END
            LIMIT 1
            """
            
            # 参数准备
            exact_name = weapon_name
            exact_type = weapon_type
            fuzzy_name = f"%{weapon_name}%"
            fuzzy_type = f"%{weapon_type}%"
            
            params = [
                # WHERE条件参数
                exact_name, exact_type,
                fuzzy_name, exact_type,
                exact_name, fuzzy_type,
                fuzzy_name, fuzzy_type,
                # ORDER BY条件参数
                exact_name, exact_type,
                fuzzy_name, exact_type,
                exact_name, fuzzy_type
            ]
            
            # 执行查询
            connection = self.db_connection.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            cursor.execute(query_sql, params)
            result = cursor.fetchone()
            
            cursor.close()
            self.db_connection.return_connection(connection)
            
            if result:
                # 处理JSON字段
                if result.get('terrain_adaptability'):
                    try:
                        result['terrain_adaptability'] = json.loads(result['terrain_adaptability'])
                    except:
                        pass
                
                if result.get('weather_limitations'):
                    try:
                        result['weather_limitations'] = json.loads(result['weather_limitations'])
                    except:
                        pass
                
                if result.get('effective_against'):
                    try:
                        result['effective_against'] = json.loads(result['effective_against'])
                    except:
                        pass
                
                if result.get('vulnerable_to'):
                    try:
                        result['vulnerable_to'] = json.loads(result['vulnerable_to'])
                    except:
                        pass
                
                if result.get('technical_specs'):
                    try:
                        result['technical_specs'] = json.loads(result['technical_specs'])
                    except:
                        pass
                
                # 转换Decimal类型
                result = self._convert_decimal_to_float(result)

                self.logger.info(f"武器知识库查询成功 - {weapon_name}({weapon_type})")
                return result
            else:
                self.logger.warning(f"武器知识库中未找到 - {weapon_name}({weapon_type})")
                return {
                    "status": "not_found",
                    "weapon_data": None,
                    "message": f"武器知识库中未找到 {weapon_name}({weapon_type})"
                }
                
        except Exception as e:
            self.logger.error(f"武器知识库查询失败: {str(e)}")
            raise Exception(f"数据库查询错误: {str(e)}")
    
    def query_counter_relationships(self, weapon_type: str) -> List[Dict[str, Any]]:
        """
        查询武器克制关系
        
        Args:
            weapon_type: 武器类型
            
        Returns:
            克制关系列表
        """
        try:
            # 查询该武器类型能够克制的目标
            effective_query = """
            SELECT target_type, effectiveness_score, engagement_range, 
                   success_probability, recommended_tactics
            FROM weapon_counter_matrix 
            WHERE attacker_type = %s OR attacker_type LIKE %s
            ORDER BY effectiveness_score DESC
            """
            
            # 查询该武器类型的弱点
            vulnerable_query = """
            SELECT attacker_type, effectiveness_score, engagement_range, 
                   success_probability, recommended_tactics
            FROM weapon_counter_matrix 
            WHERE target_type = %s OR target_type LIKE %s
            ORDER BY effectiveness_score DESC
            """
            
            connection = self.db_connection.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 查询有效对抗目标
            cursor.execute(effective_query, [weapon_type, f"%{weapon_type}%"])
            effective_against = cursor.fetchall()
            
            # 查询弱点
            cursor.execute(vulnerable_query, [weapon_type, f"%{weapon_type}%"])
            vulnerable_to = cursor.fetchall()
            
            cursor.close()
            self.db_connection.return_connection(connection)
            
            result = {
                "effective_against": effective_against,
                "vulnerable_to": vulnerable_to
            }

            # 转换Decimal类型
            result = self._convert_decimal_to_float(result)
            return result
            
        except Exception as e:
            self.logger.error(f"克制关系查询失败: {str(e)}")
            return {"effective_against": [], "vulnerable_to": []}
    
    def query_friendly_forces(self) -> List[Dict[str, Any]]:
        """
        查询我方部队信息
        
        Returns:
            我方部队列表
        """
        try:
            query_sql = """
            SELECT * FROM friendly_forces 
            WHERE operational_status = 'ready'
            ORDER BY unit_type, unit_id
            """
            
            connection = self.db_connection.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            cursor.execute(query_sql)
            results = cursor.fetchall()
            
            cursor.close()
            self.db_connection.return_connection(connection)
            
            # 处理JSON字段
            for result in results:
                if result.get('weapon_systems'):
                    try:
                        result['weapon_systems'] = json.loads(result['weapon_systems'])
                    except:
                        pass

            # 转换Decimal类型
            results = [self._convert_decimal_to_float(result) for result in results]
            return results
            
        except Exception as e:
            self.logger.error(f"我方部队查询失败: {str(e)}")
            return []
    
    def process_battlefield_stream(self, json_data: Union[str, Dict]) -> Dict[str, Any]:
        """
        处理战场流式数据
        
        Args:
            json_data: 战场JSON数据
            
        Returns:
            处理结果，包含武器信息和克制关系
        """
        start_time = time.time()
        self.stats["total_processed"] += 1
        
        try:
            # 解析JSON数据
            battlefield_data = self.parse_battlefield_json(json_data)
            
            # 查询武器知识库
            weapon_info = self.query_weapon_knowledge(
                battlefield_data["weapon_name"], 
                battlefield_data["weapon_type"]
            )
            
            # 查询克制关系
            counter_info = self.query_counter_relationships(battlefield_data["weapon_type"])
            
            # 查询我方部队信息
            friendly_forces = self.query_friendly_forces()
            
            # 构建结果
            result = {
                "processing_id": f"proc_{int(time.time() * 1000)}",
                "timestamp": time.time(),
                "processing_time": round((time.time() - start_time) * 1000, 2),
                "status": "success",
                
                # 原始战场数据
                "battlefield_data": battlefield_data,
                
                # 武器知识库信息
                "weapon_knowledge": weapon_info,
                
                # 克制关系信息
                "counter_relationships": counter_info,
                
                # 我方部队信息
                "friendly_forces": friendly_forces,
                
                # 分析标记
                "analysis_ready": True if weapon_info else False,
                "threat_level": self._assess_initial_threat(weapon_info, battlefield_data),
                
                # 元数据
                "data_completeness": {
                    "has_weapon_info": weapon_info is not None,
                    "has_counter_info": len(counter_info.get("effective_against", [])) > 0,
                    "has_friendly_forces": len(friendly_forces) > 0,
                    "has_location": bool(battlefield_data.get("latitude") and battlefield_data.get("longitude"))
                }
            }
            
            self.stats["successful_queries"] += 1
            
            self.logger.info(f"战场数据处理完成 - ID: {battlefield_data.get('id')}, "
                           f"武器: {battlefield_data['weapon_name']}, "
                           f"处理时间: {result['processing_time']}ms")
            
            return result
            
        except Exception as e:
            self.stats["failed_queries"] += 1
            self.logger.error(f"战场数据处理失败: {str(e)}")
            
            return {
                "processing_id": f"proc_{int(time.time() * 1000)}",
                "timestamp": time.time(),
                "processing_time": round((time.time() - start_time) * 1000, 2),
                "status": "error",
                "error_message": str(e),
                "battlefield_data": None,
                "weapon_knowledge": {"status": "error", "weapon_data": None},
                "counter_relationships": None,
                "friendly_forces": None,
                "analysis_ready": False
            }
    
    def _assess_initial_threat(self, weapon_info: Optional[Dict], battlefield_data: Dict) -> str:
        """
        初步威胁评估
        
        Args:
            weapon_info: 武器信息
            battlefield_data: 战场数据
            
        Returns:
            威胁等级
        """
        if not weapon_info:
            return "未知威胁"
        
        # 根据武器类型和评级进行初步威胁评估
        weapon_type = battlefield_data.get("weapon_type", "")
        firepower = weapon_info.get("firepower_rating", 5)
        protection = weapon_info.get("protection_rating", 5)
        
        # 检查是否为敌方
        faction = battlefield_data.get("faction", "")
        if faction and "敌" in faction:
            threat_multiplier = 1.2
        else:
            threat_multiplier = 1.0
        
        # 计算威胁分数
        threat_score = (firepower + protection) * threat_multiplier
        
        if threat_score >= 16:
            return "极高威胁"
        elif threat_score >= 12:
            return "高威胁"
        elif threat_score >= 8:
            return "中等威胁"
        else:
            return "低威胁"
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats["start_time"]
        
        return {
            "uptime_seconds": round(uptime, 2),
            "total_processed": self.stats["total_processed"],
            "successful_queries": self.stats["successful_queries"],
            "failed_queries": self.stats["failed_queries"],
            "success_rate": round(
                (self.stats["successful_queries"] / max(self.stats["total_processed"], 1)) * 100, 2
            ),
            "processing_rate": round(
                self.stats["total_processed"] / max(uptime, 1), 2
            )
        }
