"""
数据库配置管理模块
"""
import json
import os
from typing import Dict, Any


class DatabaseConfig:
    """数据库配置管理类"""
    
    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化数据库配置
        
        Args:
            config_file: 配置文件路径
        """
        self.config_file = config_file
        self._config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        """
        加载配置文件
        
        Returns:
            配置字典
        """
        try:
            if not os.path.exists(self.config_file):
                raise FileNotFoundError(f"配置文件 {self.config_file} 不存在")
            
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            return config
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")
    
    def get_database_config(self) -> Dict[str, Any]:
        """
        获取数据库配置
        
        Returns:
            数据库配置字典
        """
        return self._config.get('database', {})
    
    def get_connection_pool_config(self) -> Dict[str, Any]:
        """
        获取连接池配置
        
        Returns:
            连接池配置字典
        """
        return self._config.get('connection_pool', {})
    
    def get_config(self, key: str, default=None) -> Any:
        """
        获取指定配置项
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        return self._config.get(key, default)
