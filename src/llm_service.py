"""
新的LLM服务模块
支持OpenAI兼容接口和本地模型的统一接口
"""
import json
import time
import random
import logging
from typing import Dict, Any, Optional
import requests


class LLMService:
    """LLM服务类 - 支持OpenAI兼容接口和本地模型"""
    
    def __init__(self, config: Optional[Dict] = None):
        """
        初始化LLM服务
        
        Args:
            config: LLM配置信息
        """
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 加载配置
        try:
            # 总是从配置文件加载基础配置
            with open("config/llm_config.json", "r", encoding="utf-8") as f:
                llm_config = json.load(f)

                # 确定使用哪个提供商
                if config and "provider" in config:
                    provider_name = config["provider"]
                else:
                    provider_name = llm_config["default_provider"]

                # 从配置文件获取提供商配置
                if provider_name in llm_config["llm_providers"]:
                    self.config = llm_config["llm_providers"][provider_name].copy()
                    self.config["provider"] = provider_name
                else:
                    raise ValueError(f"配置文件中未找到提供商: {provider_name}")

                # 如果传入了额外配置，覆盖默认配置
                if config:
                    for key, value in config.items():
                        if key != "provider":  # provider已经设置过了
                            self.config[key] = value

                self.fallback_provider = llm_config.get("fallback_provider")
                self.common_settings = llm_config.get("common_settings", {})

        except Exception as e:
            self.logger.warning(f"无法加载LLM配置文件: {e}，使用默认配置")
            self.config = {
                "provider": "mock",
                "model": "mock-llm",
                "max_tokens": 4000,
                "temperature": 0.3,
                "timeout": 30
            }
            self.fallback_provider = None
            self.common_settings = {}
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "total_tokens_used": 0,
            "start_time": time.time()
        }
        
        self.logger.info(f"LLM服务初始化完成 - 提供商: {self.config['provider']}")
    
    def call_openai_compatible_api(self, system_prompt: str, user_prompt: str) -> Dict[str, Any]:
        """
        调用OpenAI兼容API（支持OpenAI、各种模型厂商的兼容接口）
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            API响应结果
        """
        try:
            headers = self.config.get("headers", {}).copy()

            # 处理Authorization头，确保正确设置API密钥
            if self.config.get("api_key"):
                headers["Authorization"] = f"Bearer {self.config['api_key']}"

            # 确保Content-Type正确设置
            if "Content-Type" not in headers:
                headers["Content-Type"] = "application/json"
            
            data = {
                "model": self.config.get("model", "gpt-4"),
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": self.config.get("max_tokens", 4000),
                "temperature": self.config.get("temperature", 0.3)
            }
            
            # 支持重试机制
            retry_attempts = self.config.get("retry_attempts", 1)
            for attempt in range(retry_attempts):
                try:
                    timeout = self.config.get("timeout", 60)
                    self.logger.info(f"发送API请求 - 尝试 {attempt + 1}/{retry_attempts}, 超时设置: {timeout}s")

                    response = requests.post(
                        f"{self.config['base_url']}/chat/completions",
                        headers=headers,
                        json=data,
                        timeout=timeout
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return {
                            "status": "success",
                            "content": result["choices"][0]["message"]["content"],
                            "tokens_used": result.get("usage", {}).get("total_tokens", 0),
                            "model": result.get("model", self.config["model"])
                        }
                    else:
                        if attempt == retry_attempts - 1:  # 最后一次尝试
                            return {
                                "status": "error",
                                "error_message": f"API调用失败: {response.status_code} - {response.text}",
                                "tokens_used": 0
                            }
                        time.sleep(1)  # 重试前等待
                        
                except requests.exceptions.RequestException as e:
                    self.logger.warning(f"API请求失败 (尝试 {attempt + 1}/{retry_attempts}): {str(e)}")
                    if attempt == retry_attempts - 1:  # 最后一次尝试
                        return {
                            "status": "error",
                            "error_message": f"网络请求异常: {str(e)}",
                            "tokens_used": 0
                        }
                    # 指数退避重试策略
                    wait_time = min(2 ** attempt, 10)  # 最多等待10秒
                    self.logger.info(f"等待 {wait_time}s 后重试...")
                    time.sleep(wait_time)
                    
        except Exception as e:
            return {
                "status": "error",
                "error_message": f"OpenAI兼容API调用异常: {str(e)}",
                "tokens_used": 0
            }
    
    def call_local_model_api(self, system_prompt: str, user_prompt: str) -> Dict[str, Any]:
        """
        调用本地模型API
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            API响应结果
        """
        try:
            headers = self.config.get("headers", {}).copy()

            # 处理Authorization头，确保正确设置API密钥
            if self.config.get("api_key"):
                headers["Authorization"] = f"Bearer {self.config['api_key']}"

            # 确保Content-Type正确设置
            if "Content-Type" not in headers:
                headers["Content-Type"] = "application/json"
            
            data = {
                "model": self.config.get("model", "local-llm"),
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": self.config.get("max_tokens", 4000),
                "temperature": self.config.get("temperature", 0.3),
                "stream": False
            }
            
            # 本地模型通常需要更长的超时时间
            timeout = self.config.get("timeout", 60)
            retry_attempts = self.config.get("retry_attempts", 2)
            
            for attempt in range(retry_attempts):
                try:
                    response = requests.post(
                        f"{self.config['base_url']}/chat/completions",
                        headers=headers,
                        json=data,
                        timeout=timeout
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return {
                            "status": "success",
                            "content": result["choices"][0]["message"]["content"],
                            "tokens_used": result.get("usage", {}).get("total_tokens", 0),
                            "model": result.get("model", self.config.get("model", "unknown"))
                        }
                    else:
                        if attempt == retry_attempts - 1:
                            return {
                                "status": "error",
                                "error_message": f"本地模型API调用失败: {response.status_code} - {response.text}",
                                "tokens_used": 0
                            }
                        time.sleep(2)  # 本地模型重试间隔更长
                        
                except requests.exceptions.RequestException as e:
                    if attempt == retry_attempts - 1:
                        return {
                            "status": "error",
                            "error_message": f"本地模型网络请求异常: {str(e)}",
                            "tokens_used": 0
                        }
                    time.sleep(2)
                    
        except Exception as e:
            return {
                "status": "error",
                "error_message": f"本地模型API调用异常: {str(e)}",
                "tokens_used": 0
            }
    
    def call_mock_api(self, system_prompt: str, user_prompt: str) -> Dict[str, Any]:
        """
        模拟API调用（用于测试）
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            模拟的API响应结果
        """
        try:
            # 模拟处理时间
            processing_time = random.uniform(0.8, 1.5)
            time.sleep(processing_time)
            
            # 模拟分析结果
            mock_analysis = {
                "threat_assessment": {
                    "threat_level": random.choice(["low", "medium", "high"]),
                    "threat_score": random.randint(60, 95),
                    "primary_threats": ["火力威胁", "机动威胁", "防护能力"],
                    "assessment_confidence": random.choice(["high", "medium"])
                },
                "tactical_analysis": {
                    "enemy_strengths": ["火力强大", "装甲厚重", "机动性好"],
                    "enemy_weaknesses": ["侧面装甲薄弱", "顶部防护不足", "电子设备易干扰"],
                    "terrain_factors": ["开阔地形有利", "城市环境受限"],
                    "weather_impact": "晴朗天气有利于光学瞄准"
                },
                "recommended_strategy": {
                    "primary_approach": random.choice(["远程打击", "侧翼包围", "地面包围", "空中打击"]),
                    "key_tactics": [
                        "利用反坦克导弹远程打击",
                        "协调多方向攻击",
                        "电子干扰配合",
                        "空中火力支援"
                    ],
                    "force_requirements": {
                        "minimum_units": random.randint(2, 5),
                        "recommended_weapons": ["反坦克导弹", "主战坦克", "攻击直升机"]
                    }
                },
                "force_deployment": {
                    "primary_units": ["主战坦克连", "反坦克导弹排"],
                    "support_units": ["工程兵", "通信兵", "医疗兵"],
                    "deployment_time": f"{random.randint(15, 45)}分钟",
                    "coordination_requirements": "需要空地协调"
                },
                "risk_assessment": {
                    "casualty_risk": random.choice(["低", "中等", "高"]),
                    "mission_complexity": random.choice(["简单", "中等", "复杂"]),
                    "success_probability": f"{random.randint(70, 95)}%",
                    "mission_duration": f"{random.randint(1, 4)}小时",
                    "key_risks": ["敌方反击", "友军误伤", "装备故障"]
                },
                "success_probability": f"{random.randint(75, 90)}%"
            }
            
            return {
                "status": "success",
                "content": json.dumps(mock_analysis, ensure_ascii=False, indent=2),
                "tokens_used": random.randint(1200, 1800),
                "model": self.config.get("model", "mock-llm"),
                "provider": "mock",
                "processing_time": f"{processing_time * 1000:.2f}ms"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error_message": f"模拟API调用异常: {str(e)}",
                "tokens_used": 0
            }
    
    def analyze_battlefield_situation(self, system_prompt: str, user_prompt: str) -> Dict[str, Any]:
        """
        分析战场态势
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            
        Returns:
            分析结果
        """
        self.stats["total_requests"] += 1
        start_time = time.time()
        
        try:
            # 根据提供商选择调用方法
            if self.config["provider"] == "openai_compatible":
                self.logger.info(f"调用LLM分析 - 提供商: {self.config['provider']}")
                result = self.call_openai_compatible_api(system_prompt, user_prompt)
            elif self.config["provider"] == "local":
                self.logger.info(f"调用LLM分析 - 提供商: {self.config['provider']}")
                result = self.call_local_model_api(system_prompt, user_prompt)
            elif self.config["provider"] == "mock":
                self.logger.info(f"调用LLM分析 - 提供商: {self.config['provider']}")
                result = self.call_mock_api(system_prompt, user_prompt)
            else:
                raise ValueError(f"不支持的提供商: {self.config['provider']}")
            
            # 处理结果
            if result["status"] == "success":
                self.stats["successful_requests"] += 1
                self.stats["total_tokens_used"] += result.get("tokens_used", 0)
                
                # 尝试解析JSON结果
                try:
                    analysis_result = json.loads(result["content"])
                except json.JSONDecodeError:
                    # 如果不是JSON，保持原始文本
                    analysis_result = result["content"]
                
                processing_time = round((time.time() - start_time) * 1000, 2)
                
                self.logger.info(f"LLM分析完成 - 耗时: {processing_time}ms, 消耗tokens: {result.get('tokens_used', 0)}")
                
                return {
                    "status": "success",
                    "analysis_result": analysis_result,
                    "llm_provider": self.config["provider"],
                    "model": result.get("model", self.config.get("model", "unknown")),
                    "tokens_used": result.get("tokens_used", 0),
                    "processing_time": processing_time,
                    "timestamp": time.time()
                }
            else:
                self.stats["failed_requests"] += 1
                self.logger.error(f"LLM分析失败: {result.get('error_message')}")
                
                # 尝试使用备用提供商
                if hasattr(self, 'fallback_provider') and self.fallback_provider:
                    self.logger.info(f"尝试使用备用提供商: {self.fallback_provider}")
                    # 这里可以实现备用提供商逻辑
                
                return {
                    "status": "error",
                    "error_message": result.get("error_message"),
                    "llm_provider": self.config["provider"],
                    "processing_time": round((time.time() - start_time) * 1000, 2)
                }
                
        except Exception as e:
            self.stats["failed_requests"] += 1
            self.logger.error(f"战场态势分析异常: {str(e)}")
            return {
                "status": "error",
                "error_message": f"分析过程异常: {str(e)}",
                "processing_time": round((time.time() - start_time) * 1000, 2)
            }
    
    def test_connection(self) -> bool:
        """
        测试LLM服务连接
        
        Returns:
            连接是否成功
        """
        try:
            test_result = self.analyze_battlefield_situation(
                "你是军事分析专家",
                "请简单回复'连接测试成功'"
            )
            return test_result["status"] == "success"
        except Exception:
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取服务统计信息

        Returns:
            统计信息
        """
        runtime = time.time() - self.stats["start_time"]
        return {
            "provider": self.config["provider"],
            "model": self.config.get("model", "unknown"),
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{(self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100:.1f}%",
            "total_tokens_used": self.stats["total_tokens_used"],
            "runtime_seconds": round(runtime, 2),
            "requests_per_minute": round((self.stats["total_requests"] / max(runtime / 60, 1)), 2)
        }

    def get_service_stats(self) -> Dict[str, Any]:
        """
        获取LLM服务统计信息（为Pipeline兼容性提供）

        Returns:
            格式化的服务统计信息
        """
        runtime = time.time() - self.stats["start_time"]
        total_requests = max(self.stats["total_requests"], 1)

        return {
            "total_requests": self.stats["total_requests"],
            "successful_requests": self.stats["successful_requests"],
            "failed_requests": self.stats["failed_requests"],
            "success_rate": f"{(self.stats['successful_requests'] / total_requests) * 100:.1f}%",
            "total_tokens_used": self.stats["total_tokens_used"],
            "average_tokens_per_request": round(self.stats["total_tokens_used"] / total_requests, 0),
            "requests_per_second": round(self.stats["total_requests"] / max(runtime, 1), 2),
            "provider": self.config.get("provider", "unknown"),
            "model": self.config.get("model", "unknown"),
            "uptime_seconds": round(runtime, 2)
        }
