"""
战场AI分析Pipeline
整合数据处理、提示词构建和LLM分析的完整流水线
"""
import json
import time
from typing import Dict, Any, Union, List
from .battlefield_data_service import BattlefieldDataService
from .llm_prompt_builder import LLMPromptBuilder
from .llm_service import LLMService
import logging


class BattlefieldAIPipeline:
    """战场AI分析Pipeline"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化Pipeline
        
        Args:
            config: 配置信息
        """
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 默认配置
        default_config = {
            "database_config": "config/config.json",
            "llm_config": {
                "provider": "mock",  # 默认使用mock模式进行演示
                "model": "mock-llm",
                "max_tokens": 4000,
                "temperature": 0.3
            },
            "pipeline_config": {
                "enable_caching": True,
                "max_concurrent_requests": 5,
                "timeout_seconds": 60
            }
        }
        
        self.config = {**default_config, **(config or {})}
        
        # 初始化各个组件
        self.data_processor = BattlefieldDataService(
            self.config["database_config"]
        )
        self.prompt_builder = LLMPromptBuilder()
        self.llm_service = LLMService(self.config["llm_config"])
        
        # Pipeline统计
        self.stats = {
            "total_pipelines": 0,
            "successful_pipelines": 0,
            "failed_pipelines": 0,
            "average_processing_time": 0,
            "start_time": time.time()
        }
        
        self.logger.info("战场AI分析Pipeline初始化完成")
    
    def process_single_battlefield_data(self, battlefield_json: Union[str, Dict]) -> Dict[str, Any]:
        """
        处理单条战场数据的完整Pipeline
        
        Args:
            battlefield_json: 战场JSON数据
            
        Returns:
            完整的分析结果
        """
        pipeline_start_time = time.time()
        pipeline_id = f"pipeline_{int(time.time() * 1000)}"
        self.stats["total_pipelines"] += 1
        
        self.logger.info(f"开始Pipeline处理 - ID: {pipeline_id}")
        
        try:
            # 阶段1: 数据处理和知识库查询
            self.logger.info("阶段1: 战场数据处理和知识库查询")
            stage1_start = time.time()
            
            processed_data = self.data_processor.process_battlefield_stream(battlefield_json)
            
            if processed_data["status"] != "success":
                raise Exception(f"数据处理失败: {processed_data.get('error_message')}")
            
            stage1_time = round((time.time() - stage1_start) * 1000, 2)
            self.logger.info(f"阶段1完成 - 处理时间: {stage1_time}ms")
            
            # 阶段2: LLM提示词构建
            self.logger.info("阶段2: LLM提示词构建")
            stage2_start = time.time()
            
            prompt_data = self.prompt_builder.build_complete_prompt(processed_data)
            
            stage2_time = round((time.time() - stage2_start) * 1000, 2)
            self.logger.info(f"阶段2完成 - 处理时间: {stage2_time}ms")
            
            # 阶段3: LLM分析
            self.logger.info("阶段3: LLM战术分析")
            stage3_start = time.time()
            
            llm_analysis = self.llm_service.analyze_battlefield_situation(
                prompt_data["system_prompt"],
                prompt_data["user_prompt"]
            )
            
            if llm_analysis["status"] != "success":
                raise Exception(f"LLM分析失败: {llm_analysis.get('error_message')}")
            
            stage3_time = round((time.time() - stage3_start) * 1000, 2)
            self.logger.info(f"阶段3完成 - 处理时间: {stage3_time}ms")
            
            # 阶段4: 结果整合
            self.logger.info("阶段4: 结果整合")
            stage4_start = time.time()
            
            final_result = self._integrate_pipeline_results(
                pipeline_id, processed_data, prompt_data, llm_analysis, pipeline_start_time
            )
            
            stage4_time = round((time.time() - stage4_start) * 1000, 2)
            self.logger.info(f"阶段4完成 - 处理时间: {stage4_time}ms")
            
            # 更新统计
            self.stats["successful_pipelines"] += 1
            total_time = final_result["total_processing_time"]
            self.stats["average_processing_time"] = (
                (self.stats["average_processing_time"] * (self.stats["successful_pipelines"] - 1) + total_time) /
                self.stats["successful_pipelines"]
            )
            
            self.logger.info(f"Pipeline处理完成 - ID: {pipeline_id}, "
                           f"总处理时间: {total_time}ms")
            
            return final_result
            
        except Exception as e:
            self.stats["failed_pipelines"] += 1
            total_time = round((time.time() - pipeline_start_time) * 1000, 2)
            
            self.logger.error(f"Pipeline处理失败 - ID: {pipeline_id}, 错误: {str(e)}")
            
            return {
                "pipeline_id": pipeline_id,
                "timestamp": time.time(),
                "total_processing_time": total_time,
                "status": "pipeline_failed",
                "error_message": str(e),
                "stage_completed": "数据处理阶段",
                "battlefield_data": None,
                "llm_analysis": None,
                "tactical_recommendations": None
            }
    
    def process_battlefield_stream(self, battlefield_data_list: List[Union[str, Dict]]) -> List[Dict[str, Any]]:
        """
        批量处理战场数据流
        
        Args:
            battlefield_data_list: 战场数据列表
            
        Returns:
            批量处理结果
        """
        self.logger.info(f"开始批量Pipeline处理 - 数据量: {len(battlefield_data_list)}")
        
        batch_start_time = time.time()
        results = []
        
        for i, data in enumerate(battlefield_data_list, 1):
            self.logger.info(f"处理第 {i}/{len(battlefield_data_list)} 条数据")
            
            try:
                result = self.process_single_battlefield_data(data)
                results.append(result)
                
                # 简短延迟避免过载
                time.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"批量处理第 {i} 条数据失败: {str(e)}")
                results.append({
                    "pipeline_id": f"batch_failed_{i}",
                    "status": "failed",
                    "error_message": str(e)
                })
        
        batch_time = round((time.time() - batch_start_time) * 1000, 2)
        
        # 统计批量处理结果
        successful_count = sum(1 for r in results if r.get("status") == "pipeline_completed")
        failed_count = len(results) - successful_count
        
        self.logger.info(f"批量Pipeline处理完成 - 总时间: {batch_time}ms, "
                        f"成功: {successful_count}, 失败: {failed_count}")
        
        return results
    
    def _integrate_pipeline_results(self, pipeline_id: str, processed_data: Dict, 
                                  prompt_data: Dict, llm_analysis: Dict, 
                                  pipeline_start_time: float) -> Dict[str, Any]:
        """
        整合Pipeline各阶段结果
        
        Args:
            pipeline_id: Pipeline ID
            processed_data: 数据处理结果
            prompt_data: 提示词数据
            llm_analysis: LLM分析结果
            pipeline_start_time: Pipeline开始时间
            
        Returns:
            整合后的最终结果
        """
        total_processing_time = round((time.time() - pipeline_start_time) * 1000, 2)
        
        # 提取关键信息
        battlefield_info = processed_data.get("battlefield_data", {})
        weapon_knowledge = processed_data.get("weapon_knowledge", {})
        friendly_forces = processed_data.get("friendly_forces", [])
        llm_result = llm_analysis.get("analysis_result", {})
        
        # 构建最终结果
        final_result = {
            "pipeline_id": pipeline_id,
            "timestamp": time.time(),
            "total_processing_time": total_processing_time,
            "status": "pipeline_completed",
            
            # 原始战场数据
            "input_data": {
                "battlefield_id": battlefield_info.get("id"),
                "weapon_name": battlefield_info.get("weapon_name"),
                "weapon_type": battlefield_info.get("weapon_type"),
                "faction": battlefield_info.get("faction"),
                "location": {
                    "longitude": battlefield_info.get("longitude"),
                    "latitude": battlefield_info.get("latitude"),
                    "altitude": battlefield_info.get("altitude")
                }
            },
            
            # 知识库查询结果
            "intelligence_data": {
                "weapon_knowledge_status": weapon_knowledge.get("status"),
                "weapon_specifications": weapon_knowledge.get("weapon_data", {}),
                "counter_relationships": processed_data.get("counter_relationships", {}),
                "friendly_forces_count": len(friendly_forces),
                "threat_assessment": processed_data.get("battlefield_analysis", {})
            },
            
            # LLM分析结果
            "ai_analysis": {
                "llm_provider": llm_analysis.get("llm_provider"),
                "model": llm_analysis.get("model"),
                "tokens_used": llm_analysis.get("tokens_used"),
                "analysis_result": llm_result
            },
            
            # 战术建议摘要
            "tactical_recommendations": self._extract_tactical_summary(llm_result),
            
            # 处理性能
            "performance_metrics": {
                "data_processing_time": processed_data.get("processing_time_ms"),
                "prompt_building_time": "< 1ms",  # 提示词构建很快
                "llm_analysis_time": llm_analysis.get("processing_time_ms"),
                "total_pipeline_time": total_processing_time,
                "pipeline_efficiency": "high" if total_processing_time < 5000 else "medium"
            },
            
            # 元数据
            "metadata": {
                "pipeline_version": "1.0.0",
                "processing_stages": ["数据处理", "提示词构建", "LLM分析", "结果整合"],
                "data_sources": ["武器知识库", "我方部队信息", "克制关系矩阵"],
                "confidence_level": "high" if weapon_knowledge.get("status") == "found" else "medium"
            }
        }
        
        return final_result
    
    def _extract_tactical_summary(self, llm_result: Dict) -> Dict[str, Any]:
        """从LLM结果中提取战术建议摘要"""
        if not llm_result:
            return {"status": "no_analysis", "summary": "LLM分析结果为空"}
        
        # 如果是原始文本响应
        if "raw_response" in llm_result:
            return {
                "status": "text_response",
                "summary": llm_result["raw_response"][:500] + "..." if len(llm_result["raw_response"]) > 500 else llm_result["raw_response"]
            }
        
        # 如果是结构化响应
        summary = {
            "threat_level": llm_result.get("threat_assessment", {}).get("threat_level", "unknown"),
            "primary_strategy": llm_result.get("recommended_strategy", {}).get("primary_approach", "待定"),
            "key_tactics": llm_result.get("recommended_strategy", {}).get("key_tactics", [])[:3],
            "success_probability": llm_result.get("success_probability", "未评估"),
            "required_forces": llm_result.get("force_deployment", {}),
            "mission_duration": llm_result.get("risk_assessment", {}).get("mission_duration", "未估算"),
            "primary_risks": [
                llm_result.get("risk_assessment", {}).get("casualty_risk", "未知"),
                llm_result.get("risk_assessment", {}).get("mission_complexity", "未知")
            ]
        }
        
        return {
            "status": "structured_response",
            "summary": summary
        }
    
    def get_pipeline_stats(self) -> Dict[str, Any]:
        """获取Pipeline统计信息"""
        current_time = time.time()
        uptime = current_time - self.stats["start_time"]
        
        # 获取各组件统计
        data_processor_stats = self.data_processor.get_processing_stats()
        llm_service_stats = self.llm_service.get_service_stats()
        
        return {
            "pipeline_uptime": round(uptime, 2),
            "pipeline_stats": {
                "total_pipelines": self.stats["total_pipelines"],
                "successful_pipelines": self.stats["successful_pipelines"],
                "failed_pipelines": self.stats["failed_pipelines"],
                "success_rate": round(
                    (self.stats["successful_pipelines"] / max(self.stats["total_pipelines"], 1)) * 100, 2
                ),
                "average_processing_time": round(self.stats["average_processing_time"], 2),
                "pipelines_per_second": round(
                    self.stats["total_pipelines"] / max(uptime, 1), 2
                )
            },
            "component_stats": {
                "data_processor": data_processor_stats,
                "llm_service": llm_service_stats
            },
            "system_health": "healthy" if self.stats["failed_pipelines"] == 0 else "needs_attention"
        }
    
    def test_pipeline_connectivity(self) -> Dict[str, Any]:
        """测试Pipeline各组件连接性"""
        try:
            # 测试数据库连接
            db_status = self.data_processor.db_connection.test_connection()
            
            # 测试LLM连接
            llm_status = self.llm_service.test_connection()
            
            return {
                "database_connection": "正常" if db_status else "异常",
                "llm_service": "正常" if llm_status else "异常",
                "prompt_builder": "正常",  # 提示词构建器无需外部连接
                "overall_status": "正常" if (db_status and llm_status) else "部分异常"
            }
            
        except Exception as e:
            return {
                "database_connection": "异常",
                "llm_service": "异常",
                "prompt_builder": "异常",
                "overall_status": f"系统异常: {str(e)}"
            }
