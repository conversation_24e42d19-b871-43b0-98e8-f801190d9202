"""
数据库连接管理模块
"""
import pymysql
import mysql.connector
from mysql.connector import pooling
from typing import Optional, Dict, Any, List
import logging
from .database_config import DatabaseConfig


class DatabaseConnection:
    """数据库连接管理类"""
    
    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化数据库连接

        Args:
            config_file: 配置文件路径
        """
        # 配置日志（必须在其他操作之前）
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.config = DatabaseConfig(config_file)
        self.db_config = self.config.get_database_config()
        self.pool_config = self.config.get_connection_pool_config()
        self.connection_pool = None
        self._init_connection_pool()
    
    def _init_connection_pool(self):
        """初始化连接池"""
        try:
            pool_config = {
                'pool_name': 'mysql_pool',
                'pool_size': self.pool_config.get('max_connections', 10),
                'pool_reset_session': True,
                'host': self.db_config['host'],
                'port': self.db_config['port'],
                'user': self.db_config['user'],
                'password': self.db_config['password'],
                'database': self.db_config['database'],
                'charset': self.db_config.get('charset', 'utf8mb4'),
                'autocommit': True,
                'connection_timeout': self.pool_config.get('connection_timeout', 30)
            }

            self.connection_pool = mysql.connector.pooling.MySQLConnectionPool(**pool_config)
            self.logger.info("数据库连接池初始化成功")

        except Exception as e:
            self.logger.error(f"数据库连接池初始化失败: {str(e)}")
            raise
    
    def get_connection(self):
        """
        从连接池获取连接

        Returns:
            数据库连接对象
        """
        try:
            return self.connection_pool.get_connection()
        except Exception as e:
            self.logger.error(f"获取数据库连接失败: {str(e)}")
            raise

    def return_connection(self, connection):
        """
        将连接返回到连接池

        Args:
            connection: 数据库连接对象
        """
        try:
            if connection:
                connection.close()  # 对于连接池，close()会将连接返回池中
        except Exception as e:
            self.logger.error(f"返回数据库连接失败: {str(e)}")
    
    def execute_query(self, sql: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """
        执行查询语句
        
        Args:
            sql: SQL查询语句
            params: 查询参数
            
        Returns:
            查询结果列表
        """
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            results = cursor.fetchall()
            self.logger.info(f"查询执行成功，返回 {len(results)} 条记录")
            
            return results
            
        except Exception as e:
            self.logger.error(f"查询执行失败: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def execute_update(self, sql: str, params: Optional[tuple] = None) -> int:
        """
        执行更新语句（INSERT, UPDATE, DELETE）
        
        Args:
            sql: SQL更新语句
            params: 更新参数
            
        Returns:
            影响的行数
        """
        connection = None
        cursor = None
        
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            affected_rows = cursor.rowcount
            connection.commit()
            
            self.logger.info(f"更新执行成功，影响 {affected_rows} 行")
            return affected_rows
            
        except Exception as e:
            if connection:
                connection.rollback()
            self.logger.error(f"更新执行失败: {str(e)}")
            raise
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()
    
    def test_connection(self) -> bool:
        """
        测试数据库连接
        
        Returns:
            连接是否成功
        """
        try:
            connection = self.get_connection()
            cursor = connection.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            cursor.close()
            connection.close()
            
            self.logger.info("数据库连接测试成功")
            return True
            
        except Exception as e:
            self.logger.error(f"数据库连接测试失败: {str(e)}")
            return False
