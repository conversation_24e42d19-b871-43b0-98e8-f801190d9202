"""
查询服务模块 - 根据名称查询数据库
"""
import json
from typing import Dict, Any
from .database_connection import DatabaseConnection
import logging


class QueryService:
    """查询服务类 - 专注于根据名称查询功能"""

    def __init__(self, config_file: str = "config/config.json"):
        """
        初始化查询服务

        Args:
            config_file: 配置文件路径
        """
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)

        self.db_connection = DatabaseConnection(config_file)
    
    def parse_json_input(self, json_data: str) -> Dict[str, Any]:
        """
        解析JSON输入数据

        Args:
            json_data: JSON字符串

        Returns:
            解析后的字典
        """
        try:
            if isinstance(json_data, str):
                data = json.loads(json_data)
            else:
                data = json_data

            # 验证必要字段 - 支持name字段或names字段
            if 'name' not in data and 'names' not in data:
                raise ValueError("JSON数据中缺少'name'或'names'字段")

            return data

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {str(e)}")
            raise ValueError(f"无效的JSON格式: {str(e)}")
        except Exception as e:
            self.logger.error(f"数据解析失败: {str(e)}")
            raise
    
    def build_name_query_sql(self, name: str, table_name: str = "items") -> tuple:
        """
        构建根据名称查询的SQL语句

        Args:
            name: 物品名称
            table_name: 表名

        Returns:
            (SQL语句, 参数元组)
        """
        sql = f"SELECT name, category, description FROM {table_name} WHERE name = %s"
        params = (name,)

        return sql, params

    def build_batch_query_sql(self, names: list, table_name: str = "items") -> tuple:
        """
        构建批量查询的SQL语句

        Args:
            names: 物品名称列表
            table_name: 表名

        Returns:
            (SQL语句, 参数元组)
        """
        if not names:
            raise ValueError("名称列表不能为空")

        # 构建IN查询
        placeholders = ', '.join(['%s'] * len(names))
        sql = f"SELECT name, category, description FROM {table_name} WHERE name IN ({placeholders})"
        params = tuple(names)

        return sql, params

    def query_by_name(self, json_input: str, table_name: str = "items") -> Dict[str, Any]:
        """
        根据名称查询数据库并返回JSON格式结果

        Args:
            json_input: JSON输入数据，包含name字段
            table_name: 表名

        Returns:
            包含查询结果的JSON字典
        """
        try:
            # 解析JSON输入
            data = self.parse_json_input(json_input)

            if 'name' not in data:
                raise ValueError("JSON数据中缺少'name'字段")

            name = data['name']
            self.logger.info(f"开始根据名称查询 - 名称: {name}")

            # 构建SQL查询
            sql, params = self.build_name_query_sql(name, table_name)

            # 执行查询
            results = self.db_connection.execute_query(sql, params)

            if not results:
                self.logger.info(f"未找到名称为 '{name}' 的记录")
                return {
                    "status": "not_found",
                    "message": f"未找到名称为 '{name}' 的记录",
                    "data": None
                }

            # 取第一条记录（名称应该是唯一的）
            item = results[0]

            result_json = {
                "status": "success",
                "message": "查询成功",
                "data": {
                    "name": item.get('name'),
                    "category": item.get('category'),
                    "description": item.get('description')
                }
            }

            self.logger.info(f"查询完成，找到记录: {item.get('name')}")
            return result_json

        except Exception as e:
            self.logger.error(f"根据名称查询失败: {str(e)}")
            return {
                "status": "error",
                "message": f"查询失败: {str(e)}",
                "data": None
            }

    def query_by_names(self, json_input: str, table_name: str = "items") -> Dict[str, Any]:
        """
        根据多个名称批量查询数据库并返回JSON格式结果

        Args:
            json_input: JSON输入数据，包含names字段（列表）
            table_name: 表名

        Returns:
            包含查询结果的JSON字典
        """
        try:
            # 解析JSON输入
            data = self.parse_json_input(json_input)

            if 'names' not in data:
                raise ValueError("JSON数据中缺少'names'字段")

            names = data['names']
            if not isinstance(names, list):
                raise ValueError("'names'字段必须是列表格式")

            if not names:
                raise ValueError("'names'列表不能为空")

            self.logger.info(f"开始批量查询 - 名称数量: {len(names)}")

            # 构建SQL查询
            sql, params = self.build_batch_query_sql(names, table_name)

            # 执行查询
            results = self.db_connection.execute_query(sql, params)

            # 组织返回结果
            found_items = {}
            for item in results:
                found_items[item['name']] = {
                    "name": item['name'],
                    "category": item['category'],
                    "description": item['description']
                }

            # 检查哪些名称未找到
            not_found = [name for name in names if name not in found_items]

            result_json = {
                "status": "success",
                "message": f"批量查询完成，找到 {len(found_items)} 条记录，未找到 {len(not_found)} 条记录",
                "data": {
                    "found": found_items,
                    "not_found": not_found,
                    "total_requested": len(names),
                    "total_found": len(found_items)
                }
            }

            self.logger.info(f"批量查询完成，找到 {len(found_items)} 条记录")
            return result_json

        except Exception as e:
            self.logger.error(f"批量查询失败: {str(e)}")
            return {
                "status": "error",
                "message": f"批量查询失败: {str(e)}",
                "data": None
            }
